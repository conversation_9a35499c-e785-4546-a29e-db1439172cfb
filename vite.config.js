import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(() => {
  return {
    plugins: [react()],
    build: {
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          manualChunks: {
            treatment: ['./src/pages/admin/TreatmentOnline.jsx'],
            meditation: ['./src/pages/admin/Meditation.jsx'],
            lectures: ['./src/pages/admin/Lectures.jsx'],
          },
        },
      },
    },
  };
});