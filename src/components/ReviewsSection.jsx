import { REVIEWS_CLIENTS } from "../utils/details";
import RightPoint from "../assets/right_point.svg";
import LeftPoint from "../assets/left_point.svg";
import ReviewsTop from "../assets/reviews_top.svg";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

const ReviewsSection = () => {
    const renderStars = (count) => (
        <div className="mt-2 text-yellow-400 text-lg">
            {"★".repeat(count)}
            <span className="text-gray-300">{"★".repeat(5 - count)}</span>
        </div>
    );

    return (
        <section
            id="reviews"
            className="flex flex-col items-center justify-center px-4 py-12"
        >
            <p className="text-center text-2xl md:text-3xl font-bold mb-10">
                آراء العملاء عن التطبيق
            </p>

            <div className="relative w-full ">
                <Swiper
                    modules={[Navigation, Pagination]}
                    slidesPerView={1}
                    height={"50vh"}
                    style={{padding: "1rem"}}
                    autoplay
                    speed={2000}
                    navigation
                    centeredSlides
                    onAutoplayStart={(swiper) => {
                        swiper.autoplay.stop();
                    }}
                    pagination={{ clickable: true }}
                    loop
                    onScroll={(swiper) => {
                        swiper.update();
                    }}
                    
                    breakpoints={{
                        768: {
                            slidesPerView: 1,
                        },
                        1024: {
                            slidesPerView: 1,
                        },
                        
                    }}
                >
                    {REVIEWS_CLIENTS.map((review, index) => (
                        <SwiperSlide key={index}>
                            <div className="bg-white h-[40vh] w-[80%] mx-auto rounded-2xl shadow-lg p-6 flex flex-col justify-center items-center text-center relative">
                                <img src={LeftPoint} className="absolute bottom-0 left-0 w-8 md:w-10" alt="" />
                                <img src={RightPoint} className="absolute top-0 right-0 w-8 md:w-10" alt="" />
                                <img src={ReviewsTop} className="absolute -top-4 left-4 w-10 md:w-12" alt="" />
                                <p className="text-sm md:text-base font-medium leading-relaxed w-[40%]">
                                    {review.review}
                                </p>
                                <div className="flex gap-2 mt-4 text-gray-600">
                                    <span className="font-semibold">{review.name}</span>
                                    <span className="text-sm">{review.age} سنة</span>
                                </div>
                                {renderStars(review.rating)}
                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
        </section>
    );
};

export default ReviewsSection


// import { REVIEWS_CLIENTS } from "../utils/details";
// import RightPoint from "../assets/right_point.svg";
// import LeftPoint from "../assets/left_point.svg";
// import ReviewsTop from "../assets/reviews_top.svg";

// import { Swiper, SwiperSlide } from "swiper/react";
// import { Navigation, Pagination, Autoplay } from "swiper/modules";
// import "swiper/css";
// import "swiper/css/navigation";
// import "swiper/css/pagination";

// const ReviewsSection = () => {
//   const renderStars = (count) => (
//     <div className="mt-2 text-yellow-400 text-lg">
//       {"★".repeat(count)}
//       <span className="text-gray-300">{"★".repeat(5 - count)}</span>
//     </div>
//   );

//   return (
//     <sectionx
//       id="reviews"
//       className="flex flex-col items-center justify-center px-4 py-12"
//     >
//       <p className="text-center text-2xl md:text-3xl font-bold mb-10">
//         آراء العملاء عن التطبيق
//       </p>

//       <div className="relative w-full">
//         <Swiper
//           modules={[Navigation, Pagination, Autoplay]}
//           className="overflow-visible"                // allow slide peeking
//           slidesPerView={2}                         // show a bit of the sides
//           spaceBetween={16}
//           centeredSlides
//           centeredSlidesBounds
//           loop
//           speed={700}
//           grabCursor
//           autoplay={{
//             delay: 3000,
//             disableOnInteraction: false,
//           }}
//           navigation
//           pagination={{ clickable: true }}
//           breakpoints={{
//             640: { slidesPerView: 1.2, spaceBetween: 20 },
//             768: { slidesPerView: 1.3, spaceBetween: 16 },
//             1024: { slidesPerView: 1.4, spaceBetween: 10 },
//           }}
//         >
//           {REVIEWS_CLIENTS.map((review, index) => (
//             <SwiperSlide key={index}>
//               {/* Keep slide content narrower than the slide area so peeking is obvious */}
//               <div className="bg-white h-[40vh] max-h-[420px] w-[82%] md:w-[80%] mx-auto rounded-md shadow-lzz p-6 flex flex-col justify-center items-center text-center relative">
//                 <img src={LeftPoint} className="absolute bottom-0 left-0 w-8 md:w-10" alt="" />
//                 <img src={RightPoint} className="absolute top-0 right-0 w-8 md:w-10" alt="" />
//                 <img src={ReviewsTop} className="absolute -top-4 left-4 w-10 md:w-12" alt="" />
//                 <p className="text-sm md:text-base font-medium leading-relaxed w-full md:w-3/5">
//                   {review.review}
//                 </p>
//                 <div className="flex gap-2 mt-4 text-gray-600">
//                   <span className="font-semibold">{review.name}</span>
//                   <span className="text-sm">{review.age} سنة</span>
//                 </div>
//                 {renderStars(review.rating)}
//               </div>
//             </SwiperSlide>
//           ))}
//         </Swiper>
//       </div>
//     </sectionx>
//   );
// };

// export default ReviewsSection;