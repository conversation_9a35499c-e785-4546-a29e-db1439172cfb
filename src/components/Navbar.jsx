import logo from '../assets/arabSBT_icon.png'
const Navbar = () => {
  return (
    <div className='absolute top-0 left-0 right-0 flex justify-between items-center px-[7%] py-[1.2%] bg-[#F5F5F5]'>
        <div className='text-2xl font-bold text-black flex items-center space-x-2'>
            <img src={logo} alt="" className='w-20 h-20 object-cover' />
            <h1>Arab<span className='text-[#51B4E2]'>CBT</span></h1>
        </div>
        <div className='flex space-x-4'>
          <button className='bg-[#51B4E2] text-white px-4 py-2 rounded-full' onClick={() => window.location.href = '/login'}>Login</button>
        </div>
    </div>
  )
}

export default Navbar