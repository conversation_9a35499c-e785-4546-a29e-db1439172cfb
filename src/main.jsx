import { StrictMode } from 'react'
import { I18nextProvider } from 'react-i18next';
import { createRoot } from 'react-dom/client'
import App from './App.jsx'
import './index.css'
import i18n from "./i18n"
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom"
import { UserProvider } from "./utils/UserContext";
import AOS from "aos"


AOS.init();
createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <UserProvider>
        <I18nextProvider i18n={i18n}>
            <App />
        </I18nextProvider>
      </UserProvider>
    </BrowserRouter>
  </StrictMode>,
)