import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import HttpBackend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { baseURL } from './utils/instance_axios';

i18n
  .use(HttpBackend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: 'ar', // <-- Force Arabic for now
    fallbackLng: 'ar',
    supportedLngs: ['ar', 'en'],
    nonExplicitSupportedLngs: true,
    debug: true,
    defaultNS: 'translation',
    ns: ['translation'],
    saveMissing: false,
    backend: {
      loadPath: `${baseURL}/locales/{{lng}}/translate.json`,
    },
    detection: {
      order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage', 'cookie'],
    },
    react: {
      useSuspense: false, // Important for debugging
    },
    interpolation: {
      escapeValue: false,
    },
  });
export default i18n;
