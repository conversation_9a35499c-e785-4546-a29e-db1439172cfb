@import url('https://fonts.googleapis.com/css2?family=Pacifico&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON>jawal'; /* Added fallback font for better compatibility */
    
}

/* Responsive Styling Adjustments */
/* שמור תמיד מקום לסקְרוֹל־בר האנכי כדי שלא תהיה קפיצה בפריסה */
html {
  height: 100%;
  overflow-y: scroll;              /* תמיד יש סקְרוֹל אנכי */
  scrollbar-gutter: stable both-edges; /* שומר מרווח קבוע לסקְרוֹל־בר */
  overflow-x: hidden;             /* מונע סקְרוֹל אופקי של העמוד */
}

/* For WebKit browsers (Chrome, Edge, Safari) */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f8f9fa;
}

::-webkit-scrollbar-thumb {
    background-color: #51B4E2;
    border-radius: 4px;
}

/* Body Styling */
body {
    font-smooth: antialiased; /* Enhances font rendering */
    -webkit-font-smoothing: antialiased; /* For WebKit browsers */
    -moz-osx-font-smoothing: grayscale; /* For macOS systems */
    -webkit-text-size-adjust: 100%; /* Prevents automatic text resizing on mobile */
    -ms-text-size-adjust: 100%; /* For older IE versions */
    color: #212529; /* Default text color for better contrast */
    height: 100%;
    overflow-y: scroll;              /* תמיד יש סקְרוֹל אנכי */
    scrollbar-gutter: stable both-edges; /* שומר מרווח קבוע לסקְרוֹל־בר */
    overflow-x: hidden;             /* מונע סקְרוֹל אופקי של העמוד */

}


/* Focus Styles for Accessibility */
button:focus, input:focus, textarea:focus, select:focus {
    outline: 2px solid #51B4E2; /* Adds a visible focus indicator for accessibility */
    outline-offset: 2px;
}



#paypal-button-container iframe {
  max-width: 100% !important;
}


@keyframes fade-in {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}


/* Hide scrollbar utility */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}
.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}