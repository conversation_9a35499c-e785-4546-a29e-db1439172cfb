// hooks/useAuth.js
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [role, setRole] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    const userRole = localStorage.getItem('role');

    if (token && userRole) {
      setIsAuthenticated(true);
      setRole(userRole);
    } else {
      setIsAuthenticated(false);
      setRole(null);
    }
  }, []);

  // Redirect based on role
  useEffect(() => {
    if (isAuthenticated && role) {
      if (role === 'admin') {
        navigate('/dashboard');
      } else if (role === 'user') {
        navigate('/home');
      }
    } else {
      navigate('/login');
    }
  }, [isAuthenticated, role, navigate]);

  return { isAuthenticated, role };
};

export default useAuth;
