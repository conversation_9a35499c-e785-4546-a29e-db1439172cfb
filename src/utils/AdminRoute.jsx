/* eslint-disable react/prop-types */
// src/routes/AdminRoute.jsx
import { Navigate } from 'react-router-dom';
import { useUser } from '../utils/UserContext';

export default function AdminRoute({ children }) {
  const { user } = useUser();

  /* true for roles === 'admin' OR ['admin', …] */
  const isAdmin =
    user?.roles === 'admin' ||
    (Array.isArray(user?.roles) && user.roles.includes('admin'));

  return isAdmin ? children : <Navigate to="/home" replace />;
}