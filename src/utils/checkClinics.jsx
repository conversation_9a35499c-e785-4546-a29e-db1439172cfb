/* eslint-disable react/prop-types */
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// Define clinic IPs
export const clinicIPs = [
    '*************', 
    '***********', 
    '*************', 
    '***********', 
    '***************', 
    '************', 
    '***************'
];

const IPCheckComponent = ({ clinicIPs = [] }) => {
    const navigate = useNavigate();

    useEffect(() => {
        const checkUserIP = async () => {
            try {
                const response = await axios.get('https://api.ipify.org?format=json');
                const userIP = response.data.ip;

                // Check if the IP matches the clinic's IP range
                const isClinicIP = clinicIPs.includes(userIP);
                navigate(isClinicIP ? '/login' : '/home'); // Navigate based on IP match
            } catch (error) {
                console.error('Error fetching IP address:', error);
                navigate('/login'); // Default to login on error
            }
        };

        checkUserIP();
    }, [clinicIPs, navigate]);

    return null; // This component doesn't render anything visible
};

export default IPCheckComponent;
