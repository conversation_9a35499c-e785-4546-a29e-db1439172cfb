import axios from 'axios';

// Backend URL
// const baseURL = 'http://localhost:2402'; // For local development
const baseURL = 'https://api.arab-cbt.com'; // Production URL

// Create an axios instance
const axiosInstance = axios.create({
  baseURL,

});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Add Authorization header if token exists
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Only set Content-Type if not multipart/form-data
    if (
      !config.headers['Content-Type'] &&
      !(config.data instanceof FormData)
    ) {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      // Handle unauthorized
      if (error.response.status === 401) {
        localStorage.removeItem('token');
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      }
      console.error(`API Error ${error.response.status}:`, error.response.data);
    } else {
      console.error('Network error or server not reachable');
    }

    return Promise.reject(error);
  }
);

export { baseURL };
export default axiosInstance;