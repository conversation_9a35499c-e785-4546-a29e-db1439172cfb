export const HEADER_OFFSET = 80;

export function scrollToSection(hash, container) {
  const id = (hash || '').replace('#', '');
  const el = document.getElementById(id);
  if (!el) return;

  // default to page scroll if no container given
  const scroller =
    container ||
    document.scrollingElement ||
    document.documentElement;

  // position relative to the scroller
  const scrollerTop = scroller.getBoundingClientRect
    ? scroller.getBoundingClientRect().top
    : 0;

  const targetTop =
    el.getBoundingClientRect().top    // element relative to viewport
    - scrollerTop                     // normalize to container
    + scroller.scrollTop              // current container scroll
    - HEADER_OFFSET;                  // keep clear of fixed header

  scroller.scrollTo({ top: targetTop, behavior: 'smooth' });
}