/* eslint-disable react-refresh/only-export-components */
import { createContext, useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(() => {
    const savedUser = localStorage.getItem('userData');
    return savedUser ? JSON.parse(savedUser) : null;
  });

  // const [userSubcription, setUserSubcription] = useState(() => {
  //   const savedUserSubcription = localStorage.getItem('userSubcription');
  //   return savedUserSubcription ? JSON.parse(savedUserSubcription) : null;
  // });
  const navigate = useNavigate();

  const handleUserChange = (newUser) => {
    localStorage.setItem('userData', JSON.stringify(newUser));
    setUser(newUser);
  };

  const handleLogin = (token, userData) => {
    // Store the token and user data upon successful login
    localStorage.setItem('token', token);
    localStorage.setItem('userData', JSON.stringify(userData));
    setUser(userData);

    // Store the user's role in localStorage
    localStorage.setItem('role', userData.roles);

    // Check if the user has an admin role
    if (userData.roles.includes('admin')) {
        // If the user is admin, navigate to dashboard
        navigate('/dashboard');
    } else {
        // Otherwise, navigate to the home page or any other route
        navigate('/home');
    }
};


  const handleLogout = () => {
    localStorage.removeItem('userData');
    localStorage.removeItem('token');
    localStorage.removeItem('expiresAt');
    localStorage.removeItem('role');
    setUser(null);
    navigate('/home');
  };

  return (
    <UserContext.Provider value={{ user, handleUserChange, handleLogout, handleLogin }}>
      {children}
    </UserContext.Provider>
  );
};

UserProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useUser = () => useContext(UserContext);
