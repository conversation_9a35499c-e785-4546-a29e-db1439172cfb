import { useParams } from 'react-router-dom';
import { useState } from 'react';
import axiosInstance from '../utils/instance_axios';

const ResetPassword = () => {
  const { token } = useParams();
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handlePasswordReset = async (e) => {
    e.preventDefault();
    
    if (newPassword !== confirmPassword) {
      setErrorMessage("Passwords do not match.");
      return;
    }

    try {
      const response = await axiosInstance.post(`/auth/reset-password`, { token, newPassword });
      if (response.status === 200) {
        setSuccessMessage("Password successfully reset.");
      } else {
        setErrorMessage("Failed to reset password.");
      }
    } catch (error) {
      console.error(error);
      setErrorMessage("Error occurred during password reset.");
    }
  };

  return (
    <div>
      <h2>Reset Your Password</h2>
      {errorMessage && <p className="error">{errorMessage}</p>}
      {successMessage && <p className="success">{successMessage}</p>}
      <form onSubmit={handlePasswordReset}>
        <input
          type="password"
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
          placeholder="Enter new password"
          required
        />
        <input
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          placeholder="Confirm new password"
          required
        />
        <button type="submit">Reset Password</button>
      </form>
    </div>
  );
};

export default ResetPassword;
