import { useState, useEffect } from 'react';
import axiosInstance from '../../utils/instance_axios';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import './style.css';

const Questionnaire = () => {
    const { thoughtId } = useParams();
    const { t } = useTranslation();
    const [currentQuestion, setCurrentQuestion] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [answers, setAnswers] = useState([]); // State to store answers
    const [isComplete, setIsComplete] = useState(false); // Track if questionnaire is complete

    // Fetch the initial question
    const fetchQuestionById = async (id) => {
        if (!id) {
            console.error('Invalid question ID: ID is undefined or null');
            setIsComplete(true);
            return;
        }
    
        try {
            setIsLoading(true);
            const response = await axiosInstance.get(`/questions/${thoughtId}/${id}`);
            const question = response.data;
    
            setCurrentQuestion({
                id: question.id,
                question_text: question.question_text || t('question_text_missing'),
                question_type: question.question_type || 'unknown',
                yes_route_id: question.yes_route_id ?? null,
                no_route_id: question.no_route_id ?? null,
                is_positive_route: question.is_positive_route ?? false,
            });
    
            setIsComplete(false); // Reset completion state if a new question is loaded
        } catch (error) {
            console.error(`Error retrieving question with ID ${id}:`, error.message);
            setCurrentQuestion(null);
            setIsComplete(true); // Mark questionnaire as complete if question cannot be fetched
        } finally {
            setIsLoading(false);
        }
    };
    

    // Save answers to the backend
    const saveResults = async () => {
        try {
            await axiosInstance.post('/answers', { thoughtId, answers });
            toast.success(t('results_saved_successfully'));
        } catch (error) {
            console.error('Error saving results:', error.message);
            toast.error(t('failed_to_save_results'));
        }
    };

    // Handle Yes/No response
    const handleResponse = (routeId, answerText) => {
        // Save the current answer
        setAnswers((prevAnswers) => [
            ...prevAnswers,
            { questionId: currentQuestion.id, question: currentQuestion.question_text, answer: answerText },
        ]);

        if (routeId) {
            fetchQuestionById(routeId);
        } else {
            setIsComplete(true); // Mark as complete if no route ID
            console.log(t('no_more_questions'));
        }
    };

    // Trigger saveResults when the questionnaire is complete
    useEffect(() => {
        if (isComplete && answers.length > 0) {
            saveResults();
        }
    }, [isComplete]);

    // Load the first question on mount
    useEffect(() => {
        fetchQuestionById(3); // Start with ID 3 (as per your dataset)
    }, []);

    if (isLoading) {
        return <p>{t('loading')}...</p>;
    }

    if (isComplete) {
        return (
            <div className="results-container">
                <h2>{t('questionnaire_complete')}</h2>
                <pre className="results-json">{JSON.stringify(answers, null, 2)}</pre>
                <p>{t('thank_you_for_participating')}</p>
            </div>
        );
    }

    return (
        <div className="question-container">
            {currentQuestion ? (
                <div className="question-card">
                    <p className="question-text">{currentQuestion.question_text}</p>
                    {currentQuestion.question_type !== 'end' && (
                        <div className="response-buttons">
                            <button
                                onClick={() => handleResponse(currentQuestion.yes_route_id, t('yes'))}
                                className="mr-2 yes-button"
                            >
                                {t('yes')}
                            </button>
                            <button
                                onClick={() => handleResponse(currentQuestion.no_route_id, t('no'))}
                                className="no-button"
                            >
                                {t('no')}
                            </button>
                        </div>
                    )}
                </div>
            ) : (
                <p>{t('no_questions_available')}</p>
            )}
        </div>
    );
};

export default Questionnaire;
