import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axiosInstance from '../utils/instance_axios';
import Logo from '../assets/arab_cbt_logo.png';
import { useUser } from '../utils/UserContext';
import CustomInput from '../widgets/CustomInput';
import { MdEmail, MdLock } from 'react-icons/md';

const Login = () => {
    const { t, i18n } = useTranslation();
    const { handleLogin } = useUser();
    const isRtl = i18n.language === 'ar' || i18n.language === 'he';
    const navigate = useNavigate();

    const [error, setError] = useState('');
    const [rememberMe, setRememberMe] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [fields, setFields] = useState({
        email: { value: '', focused: false },
        password: { value: '', focused: false },
    });

    // Update field state dynamically
    const handleFieldChange = (field, value) => {
        setFields((prev) => ({
            ...prev,
            [field]: { value, focused: value !== '' },
        }));
    };


    // Save the current path to redirect after login
    useEffect(() => {
        const currentPath = window.location.pathname;
        if (currentPath !== '/login' && !localStorage.getItem('redirectPath')) {
            localStorage.setItem('redirectPath', currentPath);
        }
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');

        const email = fields.email.value.trim().toLowerCase();
        const password = fields.password.value;

        if (!email || !password) {
            setError(t('please_fill_all_fields'));
            return;
        }

        try {
            setIsLoading(true);

            const { data: res } = await axiosInstance.post('/auth/login', { email, password });
            const { token, data: userData, expiresAt } = res;

            /* remember-me expiry */
            const store = rememberMe ? localStorage : sessionStorage;
            store.setItem('expiresAt', expiresAt);

            /* save user & token, get admin flag */
            const isAdmin = handleLogin(token, userData);   // handleLogin RETURNS true/false

            /* navigate */
            navigate(isAdmin ? '/dashboard' : '/home', { replace: true });
        } catch (err) {
            const st = err.response?.status;
            console.error('[LOGIN]', st, err.response?.data || err.message);

            if (st === 403) setError(t('error_account_inactive'));
            else if (st === 400 || st === 401) setError(t('error_invalid_credentials'));
            else if (st === 404) setError(t('endpoint_not_found'));
            else setError(t('something_went_wrong'));
        } finally {
            setIsLoading(false);
        }
    };

    const isFormValid = fields.email.value?.trim?.() !== '' && fields.password.value?.trim?.() !== '';

    return (
        <div className="m-0 p-0 h-screen w-full flex items-center justify-center" dir={isRtl ? 'rtl' : 'ltr'}>
            <div className="w-full max-w-[300px] md:max-w-[500px] lg:max-w-[600px] h-full max-h-[600px] xs:max-h-[800px] rounded-3xl shadow-2xl shadow-[#51B4E2] bg-[#51B4E2] flex flex-col relative">
                <div className="h-1/2 rounded-tr-3xl rounded-tl-3xl bg-[#51B4E2]">
                    <div className="text-3xl mt-10 font-bold text-white flex flex-col justify-center items-center">
                        <img src={Logo} className="w-[110px] h-[110px] xs:w-[150px] xs:h-[150px]  object-cover" alt="ArabCBT Logo" />
                        <span className="text-[30px] font-bold text-white">{t('name_app_1') + ' ' + t('name_app_2')}</span>
                        <span className="text-[10px] text-white mt-2">{t('subtitle_login_account')}</span>
                    </div>
                </div>
                <div className="h-1/2 rounded-br-3xl rounded-bl-3xl bg-white"></div>
                <div className="absolute top-[280px] left-[50%] translate-x-[-50%] w-full max-w-[250px] md:max-w-[400px] lg:max-w-[500px] h-auto shadow-2xl shadow-slate-400 flex items-center justify-center rounded-3xl bg-white">
                    <form onSubmit={handleSubmit} className="w-full px-4 py-4 flex flex-col gap-2 items-center">
                        {error && <div className="text-red-500">{error}</div>}
                        <CustomInput
                            type="email"
                            id="email"
                            name="email"
                            label={t('email')}
                            isRTL={isRtl}
                            focused={fields.email.focused}
                            setFocused={(id, focused) =>
                                setFields((prev) => ({
                                    ...prev,
                                    [id]: { ...prev[id], focused }
                                }))
                            }
                            Icon={<MdEmail />}
                            value={fields.email.value}
                            onChange={(id, value) => handleFieldChange(id, value)}
                            placeholder={fields.email.focused ? t('enterEmail') : ''}
                            className="p-3 w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#51B4E2]"
                        />

                        <CustomInput
                            type="password"
                            id="password"
                            name="password"
                            label={t('password')}
                            isRTL={isRtl}
                            focused={fields.password.focused}
                            setFocused={(id, focused) =>
                                setFields((prev) => ({
                                    ...prev,
                                    [id]: { ...prev[id], focused }
                                }))
                            }
                            Icon={<MdLock />}
                            value={fields.password.value}
                            onChange={(id, value) => handleFieldChange(id, value)}
                            placeholder={fields.password.focused ? t('enterPassword') : ''}
                            className="p-3 w-full text-[10px] md:text-[14px] rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#51B4E2]"
                        />
                        <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-1 cursor-pointer text-[10px] md:text-[14px]">
                                <input
                                    type="checkbox"
                                    id="remember"
                                    name="remember"
                                    checked={rememberMe}
                                    onChange={(e) => setRememberMe(e.target.checked)}
                                />
                                <label htmlFor="remember">
                                    {t('remember_me')}
                                </label>
                            </div>

                            <Link to="/forgot-password" className="text-[#51B4E2] text-[10px] md:text-[14px] font-semibold hover:underline cursor-pointer ">
                                {t('forgot_password')}
                            </Link>
                        </div>

                        <div className="text-center flex items-center justify-start gap-2 w-full cursor-pointer" onClick={() => navigate('/register')}>
                            <p className=' text-[12px] md:text-[14px] '>{t('dont_have_account')}</p>
                            <p className="text-[#51B4E2] font-semibold text-[12px] md:text-[14px] hover:underline">
                                {t('create_account_btn')}
                            </p>
                        </div>

                        <button
                            type="submit"
                            className="w-full mt-6 py-3 bg-[#51B4E2] text-[14px] md:text-[16px]  text-white font-bold rounded-md hover:bg-[#419cc0] transition duration-300"
                            disabled={!isFormValid || isLoading}
                        >
                            {isLoading ? t('loading') : t('login')}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Login;
