import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Logo from '../assets/arab_cbt_logo.png';
import { useTranslation } from 'react-i18next';

const Splash = () => {
  const { t } = useTranslation();
  const [progress, setProgress] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    const progressTimer = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress >= 100) {
          clearInterval(progressTimer);
          return 100;
        }
        return prevProgress + 5;
      });
    }, 159);

    const redirectTimer = setTimeout(() => {
      navigate('/home');
    }, 3000);

    return () => {
      clearInterval(progressTimer);
      clearTimeout(redirectTimer);
    };
  }, []);

    // const checkUserIP = async () => {
  //   try {
  //     const response = await axios.get('https://api.ipify.org?format=json');
  //     const userIP = response.data.ip;

  //     // Check if the IP matches the clinic's IP range
  //     const isClinicIP = checkClinicIP(userIP);
  //     navigate(isClinicIP ? '/login' : '/home'); // Navigate based on IP match
  //   } catch (error) {
  //     console.error('Error fetching IP address:', error);
  //     navigate('/login'); // Default to login on error
  //   }
  // };

  return (
    <div className="m-0 p-0 h-screen w-full flex flex-col justify-center items-center">
      <img src={Logo} alt="App Logo" />
      <div className="text-3xl font-bold mt-4">
        {t("name_app_1")}<span className="text-[#51B4E2]">{t("name_app_2")}</span>
      </div>
      <div className="relative w-56 mt-4 h-2 bg-gray-200 rounded">
        <div
          className="absolute top-0 left-0 h-2 rounded bg-[#51B4E2] transition-all duration-100"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    </div>
  );
};

export default Splash;
