import { useState } from 'react';
import axiosInstance from '../utils/instance_axios';
import { useTranslation } from 'react-i18next';

const Support = () => {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState('');
  const { t, i18n } = useTranslation();

  const isRTL = i18n.language === 'ar' || i18n.language === 'he';

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !message) {
      // timer out 2 seconds
      setStatus(t('enter_email_and_message'));
      setTimeout(() => {
        setStatus('');
      }, 2000);
    } else if (email === '') {
      // timer out 2 seconds
      setStatus(t('enter_email'));
      setTimeout(() => {
        setStatus('');
      }, 2000);
    } else if (message === '') {
      // timer out 2 seconds
      setStatus(t('enter_message'));
      setTimeout(() => {
        setStatus('');
      }, 2000);

      return;
    }


    try {
      const response = await axiosInstance.post('/support/send', {
        email,
        message,
      });

      if (response.status === 200) {
        // Assume your backend handles sending a WhatsApp message and returns success
        setStatus('Message sent successfully!');

        setEmail('');
        setMessage('');

        // Optional: You can add additional logic here to handle other actions after sending the message
      } else {
        setStatus('Failed to send the message. Please try again later.');
        setTimeout(() => {
          setStatus('');
        }, 2000);
      }
    } catch (error) {
      console.error('Error sending support message:', error);
      setStatus(t('error_sending_message'));
      setTimeout(() => {
        setStatus('');
      }, 2000);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen" dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="p-6 max-w-md w-full bg-white shadow-2xl shadow-black rounded-md">
        <h1 className="text-2xl font-bold mb-4">{t('contact_support')}</h1>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div className="flex flex-col">
            <label htmlFor="email" className="mb-1 font-medium">{t('email')}</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="p-2 border rounded-md"
              placeholder={t('enter_email')}
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="message" className="mb-1 font-medium">{t('message')}</label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="p-2 border rounded-md h-32"
              placeholder={t('write_message')}
            />
          </div>
          <button type="submit" className="bg-blue-500 text-white p-2 rounded-md">
            {t('send_message')}
          </button>
          {status && <div className="mt-2 text-center text-red-500">{status}</div>}
        </form>
      </div>
    </div>
  );
};

export default Support;
