import Logo from '../assets/arab_cbt_logo.png';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import axiosInstance from '../utils/instance_axios';
import { useNavigate } from 'react-router-dom';

const ForgotPassword = () => {
    const [email, setEmail] = useState('');
    const [errorMessage, setErrorMessage] = useState(null); // Error message state
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();

    const isRtl = i18n.language === 'ar' || i18n.language === 'he';

    const handleSubmit = async (e) => {
        e.preventDefault();
        setErrorMessage(null); // Clear previous errors

        try {
            const response = await axiosInstance.post('/auth/forgot-password', { email });

            if (response.status === 200) {
                navigate('/login');
                setEmail(''); // Clear the email input after successful submission
            } else {
                setErrorMessage(t('something_went_wrong'));
            }
        } catch (error) {
            console.error(error);
            setErrorMessage(t('something_went_wrong')); // Handle error and show message
        }
    };

    return (
        <div className="m-0 p-0 h-screen w-full flex items-center justify-center" dir={isRtl ? 'rtl' : 'ltr'}>
            <div className="w-[400px] md:w-1/2 lg:w-1/3 h-[800px] rounded-3xl shadow-2xl shadow-[#51B4E2] bg-[#51B4E2] flex flex-col relative">
                <div className="h-1/2 rounded-tr-3xl rounded-tl-3xl bg-[#51B4E2]">
                    <div className="text-3xl mt-10 font-bold text-white flex flex-col justify-center items-center">
                        <img src={Logo} className="w-[150px] h-[150px]" alt="ArabCBT Logo" />
                        <span className="text-4xl font-bold text-white">ArabCBT</span>
                        <span className="text-base font-medium text-white mt-2">{t('forgot_password')}</span>
                    </div>
                </div>
                <div className="h-1/2 rounded-br-3xl rounded-bl-3xl bg-white"></div>
                <div className="absolute top-[280px] left-[50%] translate-x-[-50%] w-[300px] md:w-[400px] lg:w-[500px] h-auto shadow-2xl shadow-slate-400 flex items-center justify-center rounded-3xl bg-white">
                    <form onSubmit={handleSubmit} className="w-full px-8 py-12 flex flex-col gap-6">

                        <div className="flex flex-col gap-2">
                            <label htmlFor="email" className="text-sm font-medium text-gray-600">{t('email')}</label>
                            <input
                                type="email"
                                name="email"
                                id="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)} // Handle input change
                                className="border-2 border-gray-300 rounded-md p-2"
                                placeholder={t('email')}
                                required
                            />
                        </div>

                        {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>} {/* Error message */}

                        <button type="submit" className="w-full mt-6 py-3 bg-[#51B4E2] text-white font-bold rounded-md hover:bg-[#419cc0] transition duration-300">
                            {t('send_reset_instructions')}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
