/* eslint-disable react/prop-types */
import { Navigate } from "react-router-dom";
import { isTokenValid } from "../utils/authUtils";

const ProtectedRoute = ({ children, roles }) => {
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  const userRoles = JSON.parse(localStorage.getItem('role'));

  // Redirect to login if token is missing or invalid
  if (!token || !isTokenValid()) {
    return <Navigate to="/login" />;
  }

  // Restrict access if user does not have required roles
  if (roles && !roles.some((role) => userRoles.includes(role))) {
    return <Navigate to="/home" />;
  }

  return children;
};

export default ProtectedRoute;
