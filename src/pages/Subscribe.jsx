/* eslint-disable react/prop-types */
import { useTranslation } from 'react-i18next';
import { useEffect, useMemo, useState, useLayoutEffect } from 'react';
import Check_SVG from '../assets/check.svg';
import axiosInstance from '../utils/instance_axios';
import { Footer } from './Home';
import { useUser } from '../utils/UserContext';

const PlanComponent = ({ plan, isRTL, active, allFeatures, submitHandler }) => {
    const price = active === 0 ? plan.yearly_price : plan.monthly_price;

    const featureIdsInPlan = (plan.features || []).map(f => f.feature_id);

    return (
        <div className="w-full flex flex-col items-center border border-[rgba(81,180,226,0.65)] rounded-2xl overflow-hidden shadow-[0px_0px_15px_5px_rgba(81,180,226,0.65)]">
            {/* כותרת */}
            <div className="w-full h-[200px] flex flex-col justify-center items-center bg-gradient-to-r from-[#51B4E2] to-[#3180A5] relative">
                <p className="absolute top-6 right-8 text-white text-2xl font-bold">
                    باقة
                </p>
                <h1 className="text-3xl font-bold text-white mt-4">
                    {plan.name}
                </h1>
                <div className="w-full max-w-[200px] h-[2px] bg-white/50 my-2" />
                <p className="text-xl font-bold text-white">
                    {price} $
                </p>
                <p className="w-full max-w-[320px] text-sm font-medium text-white text-center mt-2 px-2">
                    {plan.name === 'شامل'
                        ? 'هذه الباقة مناسبة لمن يرغب في التحرر تمامًا من القلق وجميع مخاوفه وأفكاره السلبية'
                        : plan.name === 'متقدم +'
                            ? 'هذه الباقة مخصصة لمن يسعى لتحقيق السلام الداخلي الكامل والاستمتاع بحياة مليئة بالتوازن والراحة'
                            : 'هذه الباقة مثالية لمن يعانون من خوف واحد فقط ويريدون التغلب عليه بسهولة'}
                </p>
            </div>

            {/* רשימת הפיצ׳רים */}
            <div className="w-full flex flex-col justify-center items-start p-4">
                {allFeatures.map((feature) => {
                    const included = featureIdsInPlan.includes(feature.feature_id);
                    return (
                        <div
                            key={feature.feature_id}
                            className="flex items-center gap-2 mb-2"
                            dir={isRTL ? 'rtl' : 'ltr'}
                        >
                            <img
                                src={Check_SVG}
                                alt={included ? "ميزة متاحة" : "ميزة غير متاحة"}
                                className={`w-5 h-5 ${included ? '' : 'opacity-30'}`}
                            />
                            <p
                                className={`text-base font-bold ${included
                                    ? 'text-[#3180A5]'
                                    : 'text-gray-400 line-through'
                                    }`}
                            >
                                {feature.feature_name}
                            </p>
                        </div>
                    );
                })}
            </div>

            {/* כפתור */}
            <div className="w-full px-4 pb-4 mt-auto">
                <button
                    className="w-full bg-[#51B4E2] hover:bg-[#3180A5] text-white font-bold py-2 rounded-full transition duration-200"
                    onClick={submitHandler}
                >
                    اشتراك
                </button>
            </div>
        </div>
    );
};

const Subscribe = () => {
    const { t, i18n } = useTranslation();
    const [active, setActive] = useState(0);
    const [plans, setPlans] = useState([]);
    const { user } = useUser();
    const fetchPlans = async () => {
        try {
            const { data } = await axiosInstance.get('/subscriptions');
            console.log("plans: ", data);
            setPlans(Array.isArray(data.packages) ? data.packages : []);
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        fetchPlans();
    }, []);

    // אוסף הפיצ׳רים המלאים (תמיד עדכני)
    const allFeatures = useMemo(() => {
        return Array.from(
            new Map(
                plans.flatMap(p => p.features || []).map(f => [f.feature_id, f])
            ).values()
        );
    }, [plans]);

    const isRTL = i18n.language === 'ar' || i18n.language === 'he';
    const handleClick = (index) => setActive(index);

    const options = [
        { label: t('yearly'), index: 0 },
        { label: t('monthly'), index: 1 },
    ];

    const handlePurchase = (plan, price) => {
        if (!window.paypal) {
            alert("PayPal SDK not loaded.");
            return;
        }

        // Create overlay
        const overlay = document.createElement("div");
        overlay.id = "paypal-overlay";
        overlay.style.position = "fixed";
        overlay.style.top = 0;
        overlay.style.left = 0;
        overlay.style.width = "100%";
        overlay.style.height = "100%";
        overlay.style.backgroundColor = "rgba(0,0,0,0.6)";
        overlay.style.display = "flex";
        overlay.style.alignItems = "center";
        overlay.style.justifyContent = "center";
        overlay.style.zIndex = "9999";
        overlay.style.padding = "1rem";
        overlay.style.boxSizing = "border-box";

        // Create modal container
        const container = document.createElement("div");
        container.id = "paypal-button-container";
        container.style.backgroundColor = "#fff";
        container.style.borderRadius = "10px";
        container.style.padding = "1.5rem";
        container.style.maxWidth = "600px";
        container.style.width = "100%";
        container.style.boxShadow = "0 0 25px rgba(0,0,0,0.3)";
        container.style.position = "relative";
        container.style.display = "grid";
        container.style.gridTemplateColumns = "1fr";
        container.style.gridGap = "1rem";
        container.style.boxSizing = "border-box";

        // Close button
        const closeButton = document.createElement("button");
        closeButton.innerHTML = "&times;";
        closeButton.style.position = "absolute";
        closeButton.style.top = "12px";
        closeButton.style.right = "16px";
        closeButton.style.background = "";
        closeButton.style.border = "none";
        closeButton.style.fontSize = "1.8rem";
        closeButton.style.cursor = "pointer";
        closeButton.onclick = () => {
            document.body.removeChild(overlay);
            document.body.style.overflow = "";
        };

        // Title
        const title = document.createElement("h2");
        title.innerText = `الدفع - ${plan.name}`;
        title.style.fontSize = "1.2rem";
        title.style.margin = "0";
        title.style.textAlign = "center";
        title.style.fontWeight = "600";
        title.style.direction = isRTL ? "rtl" : "ltr";

        // Append elements
        container.appendChild(closeButton);
        container.appendChild(title);
        overlay.appendChild(container);
        document.body.appendChild(overlay);

        // Prevent background scroll
        document.body.style.overflow = "hidden";

        // Render PayPal Buttons
        window.paypal.Buttons({
            style: {
                layout: "horizontal", // side by side
                color: "blue",
                shape: "rect",
                label: "paypal",
                tagline: false,
                height: 45,
            },
            createOrder: (data, actions) => {
                return actions.order.create({
                    purchase_units: [
                        {
                            amount: {
                                value: price,
                            },
                            description: `شراء الباقة: ${plan.name}`,
                        },
                    ],
                });
            },
            onApprove: (data, actions) => {
                return actions.order.capture().then(async function (details) {
                    try {
                        // 💡 Save subscription to DB
                        await axiosInstance.post("/subscriptions/subscribe", {
                            user_id: user.id,                         // from your context
                            subscription_type: "package",             // or "item"
                            reference_id: plan.id,                    // the ID of the purchased package
                            plan_type: active === 0 ? "yearly" : "monthly",
                            price_paid: details.purchase_units[0].amount.value,
                            currency: details.purchase_units[0].amount.currency_code || "USD",
                            payment_method_id: 1,                     // e.g., 1 = PayPal (make sure you have this in your payment_methods table)
                            payment_reference: details.id             // PayPal transaction ID
                        });

                        alert(`تمت المعاملة بنجاح بواسطة ${details.payer.name.given_name}`);
                    } catch (error) {
                        console.error("Error saving subscription:", error);
                        alert("تم الدفع لكن حدث خطأ عند حفظ الاشتراك. يرجى التواصل مع الدعم.");
                    } finally {
                        document.body.removeChild(overlay);
                        document.body.style.overflow = "";
                    }
                });
            },
            onCancel: () => {
                document.body.removeChild(overlay);
                document.body.style.overflow = "";
            },
            onError: (err) => {
                console.error("PayPal error:", err);
                document.body.removeChild(overlay);
                document.body.style.overflow = "";
                alert("حدث خطأ أثناء الدفع. حاول مرة أخرى.");
            },
        }).render("#paypal-button-container");
    };

    const [isMobile, setIsMobile] = useState();

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useLayoutEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        handleResize(); // Call immediately
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <>
            <div className="w-full py-10 flex flex-col justify-center items-center">
                <h1 className="text-2xl font-bold w-full text-center my-10">
                    معلومات عن الباقات
                </h1>
                <div className="w-full max-w-6xl mx-4 my-2 p-4 flex flex-col justify-center items-center">
                    <div className="w-full max-w-[300px] h-[40px] flex justify-center items-center border border-[#51B4E2]">
                        {options.map(({ label, index }) => (
                            <button
                                key={index}
                                type="button"
                                onClick={() => handleClick(index)}
                                className={`px-4 py-2 w-full h-full flex justify-center items-center ${active === index ? 'bg-[#51B4E2] text-white' : 'bg-white text-[#51B4E2]'}`}
                            >
                                {label}
                            </button>
                        ))}
                    </div>
                    <h3 className="text-lg font-bold text-[#51B4E2] mt-8">
                        يمكنك توفير نسبة 20% عند الاشتراك السنوي
                    </h3>

                    {/* קו עיצובי */}
                    <div className="relative w-full max-w-[80%]  h-[2px] bg-[#51B4E2] md:mt-8 hidden md:block ">
                        <div className="absolute -top-[20px] -left-2 bg-white border-2 border-[#51B4E2] rounded-full p-1">
                            <div className='w-8 h-8 bg-[#FFCA33] border-2 border-[#51B4E2] rounded-full' />
                        </div>
                        <div className="absolute -top-[20px] -right-2 bg-white border-2 border-[#51B4E2] rounded-full p-1">
                            <div className='w-8 h-8 bg-[#FFCA33] border-2 border-[#51B4E2] rounded-full' />
                        </div>
                        <div className="absolute -top-[20px] left-1/2 transform -translate-x-1/2 bg-white rotate-[-45deg] border-2 border-[#51B4E2] rounded-md p-1">
                            <div className='w-8 h-8 bg-[#FFCA33] border-2 border-[#51B4E2] rounded-md' />
                        </div>
                    </div>

                    <div className="border-2 border-[#51B4E2] rounded-full p-2 mt-14">
                        <p className='text-lg bg-gradient-to-r from-[#51B4E2] to-[#3180A5] font-bold text-white px-6 py-2 rounded-full'>
                            مقارنة الباقات
                        </p>
                    </div>

                    <div
                        className="
                    w-full
                    flex
                    gap-4
                    overflow-x-auto
                    md:grid md:grid-cols-3 md:gap-6
                    py-4
                    px-2
                    scroll-smooth
                    mt-8
                    "
                        dir={isRTL ? 'rtl' : 'ltr'}
                    >
                        {plans.map((plan, index) => (
                            <div
                                key={index}
                                className="
                        flex-shrink-0
                        w-[280px]
                        md:w-auto
                        "
                            >
                                <PlanComponent
                                    plan={plan}
                                    isRTL={isRTL}
                                    active={active}
                                    allFeatures={allFeatures}
                                    submitHandler={() => handlePurchase(plan, active === 0 ? plan.yearly_price : plan.monthly_price)}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className="w-full py-10 border-t-[1px] border-[#51B4E2] flex flex-col justify-center items-center">

                <Footer isMobile={isMobile} isRTL={isRTL} />
            </div>
        </>
    );
};

export default Subscribe;