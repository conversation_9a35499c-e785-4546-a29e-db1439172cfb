/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axiosInstance from '../../utils/instance_axios';
import TableWidget from '../../widgets/TableWidget';
import { useTranslation } from 'react-i18next';
import { MdClose } from 'react-icons/md';
import { CgTrash } from 'react-icons/cg';
import toast from 'react-hot-toast';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';

const Questions = () => {
    const { thoughtId } = useParams();
    const { t, i18n } = useTranslation();

    const [questions, setQuestions] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    // Inline row edit
    const [editingRowId, setEditingRowId] = useState(null);

    // “Add question” modal
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
        questions: [
            {
                questionText: '',
                questionType: 'question',
                yesRouteId: null,
                noRouteId: null,
                answerText: '',
                summary: '',
                buttonYesText: '',
                buttonNoText: '',
                isQuestion: false,
            },
        ],
    });

    // Options for question type
    const typeOptions = [
        { value: 'question', label: t('question') },
        { value: 'introduction', label: t('introduction') },
        { value: 'sub_question', label: t('sub_question') },
        { value: 'conclusion', label: t('conclusion') },
        { value: 'sentence', label: t('sentence') },
        { value: 'repeating_sentence', label: t('repeating_sentence') },
        { value: 'sentence_general', label: t('sentence_general') },
        { value: 'summary', label: t('summary') },
    ];

    const fetchQuestions = async () => {
        if (!thoughtId) {
            toast.error(t('missing_required_fields'));
            setIsLoading(false);
            return;
        }
        try {
            const res = await axiosInstance.get(`/questions/thought/${thoughtId}`);
            const data = Array.isArray(res.data) ? res.data : [];
            const normalized = data.map((q) => ({
                id: q.id,
                questionText: q.question_text,
                questionType: q.question_type,
                yesRouteId: q.yes_route_id,
                noRouteId: q.no_route_id,
                answerText: q.answer_text,
                summary: q.summary,
                buttonYesText: q.button_yes_text,
                buttonNoText: q.button_no_text,
                isQuestion: !!q.is_question,
            }));
            setQuestions(normalized);
        } catch (e) {
            console.error(e);
            toast.error(t('failed_to_fetch_questions'));
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchQuestions();
    }, [thoughtId]);

    // ---- Add modal handlers ----
    const handleInputChange = (e, index = 0) => {
        const { name, value, type, checked } = e.target;
        setFormData((prev) => {
            const next = { ...prev };
            next.questions = [...prev.questions];
            next.questions[index] = {
                ...next.questions[index],
                [name]: type === 'checkbox' ? checked : value,
            };
            return next;
        });
    };

    const handleRemoveQuestionField = (index) => {
        setFormData((prev) => ({
            ...prev,
            questions: prev.questions.filter((_, i) => i !== index),
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const q = formData.questions[0];
            await axiosInstance.post('/questions', {
                thoughtId,
                questions: [
                    {
                        questionText: q.questionText.trim(),
                        questionType: q.questionType,
                        answerText: q.answerText || null,
                        summary: q.summary || null,
                        yesRouteId: q.yesRouteId || null,
                        noRouteId: q.noRouteId || null,
                        buttonYesText: q.buttonYesText || null,
                        buttonNoText: q.buttonNoText || null,
                        isQuestion: q.isQuestion,
                    },
                ],
            });
            toast.success(t('question_created_successfully'));
            setShowForm(false);
            setFormData({
                questions: [
                    {
                        questionText: '',
                        questionType: 'question',
                        yesRouteId: null,
                        noRouteId: null,
                        answerText: '',
                        summary: '',
                        buttonYesText: '',
                        buttonNoText: '',
                        isQuestion: false,
                    },
                ],
            });
            fetchQuestions();
        } catch (e) {
            console.error(e);
            toast.error(e?.response?.data?.error || t('save_failed'));
        }
    };

    // ---- Inline edit helpers (updates local state) ----
    const updateQuestionField = (id, field, value) => {
        setQuestions((prev) =>
            prev.map((q) => (q.id === id ? { ...q, [field]: value } : q))
        );
    };

    // ✅ Your requested function: update question type on selection
    const handleSelect = (value, questionId) => {
        const updatedQuestions = [...questions];
        const index = updatedQuestions.findIndex((q) => q.id === questionId);
        if (index !== -1) {
            updatedQuestions[index].questionType = value;
            setQuestions(updatedQuestions);
        }
    };

    const handleSaveQuestion = async (questionId) => {
        try {
            const q = questions.find((x) => x.id === questionId);
            if (!q) {
                toast.error(t('save_failed'));
                return;
            }
            const payload = {
                questionText: q.questionText?.trim() || 'Untitled',
                questionType: q.questionType || 'question',
                yesRouteId: q.yesRouteId ?? null,
                noRouteId: q.noRouteId ?? null,
                answerText: q.answerText ?? '',
                summaryText: q.summary ?? '',
                buttonYesText: q.buttonYesText ?? '',
                buttonNoText: q.buttonNoText ?? '',
                isQuestion: q.isQuestion ? 1 : 0,
            };
            await axiosInstance.put(`/questions/update/${questionId}`, payload);
            toast.success(t('question_updated_successfully'));
            setEditingRowId(null);
            fetchQuestions();
        } catch (e) {
            console.error(e);
            toast.error(e?.response?.data?.error || t('save_failed'));
        }
    };

    // --- inside Questions component ---

    // safer map for type label
    const typeLabel = (val) => {
        const opt = typeOptions.find(o => o.value === val);
        return opt ? opt.label : val || t('question');
    };

    // ---- Table columns ----
    const columns = [
        {
            key: 'id',
            label: t('id'),
            align: 'center',
            width: 80,
            // TableWidget שלך קורא render(value, row), אז פה ניקח רק את value
            render: (value) => <div className="text-center">{value}</div>,
        },
        {
            key: 'questionText',
            label: t('question_text'),
            align: 'center',
            width: 180,
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="text"
                        value={row.questionText || ''}
                        onChange={(e) => updateQuestionField(row.id, 'questionText', e.target.value)}
                        dir={i18n.language === 'ar' || i18n.language === 'he' ? 'rtl' : 'ltr'}
                        className="border border-gray-300 rounded px-2 py-2 w-full text-right"
                        placeholder={t('question_text')}
                    />
                ) : (
                    <span className="block w-full">{value ?? '–'}</span>
                ),
        },
        {
            key: 'questionType',
            label: t('question_type'),
            align: 'center',
            width: 180,
            render: (value, row) =>
                editingRowId === row.id ? (
                    <select
                        value={row.questionType || 'question'}
                        onChange={(e) => handleSelect(e.target.value, row.id)}
                        className="border border-gray-300 rounded px-2 py-2 w-full"
                        dir={i18n.language === 'ar' || i18n.language === 'he' ? 'rtl' : 'ltr'}
                    >
                        {typeOptions.map((opt) => (
                            <option key={opt.value} value={opt.value}>
                                {opt.label}
                            </option>
                        ))}
                    </select>
                ) : (
                    <span className="block w-full">{typeLabel(value)}</span>
                ),
        },
        {
            key: 'yesRouteId',
            label: t('yes_route'),
            align: 'center',
            width: 120,
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="number"
                        value={row.yesRouteId ?? ''}
                        onChange={(e) => updateQuestionField(row.id, 'yesRouteId', e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 w-24 text-center"
                    />
                ) : (
                    <span>{value ?? '–'}</span>
                ),
        },
        {
            key: 'noRouteId',
            label: t('no_route'),
            align: 'center',
            width: 120,
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="number"
                        value={row.noRouteId ?? ''}
                        onChange={(e) => updateQuestionField(row.id, 'noRouteId', e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 w-24 text-center"
                    />
                ) : (
                    <span>{value ?? '–'}</span>
                ),
        },
        {
            key: 'answerText',
            label: t('answer_text'),
            align: 'center',
            clampLines: 2,   // ⬅️ גם פה קיצור
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="text"
                        value={row.answerText ?? ''}
                        onChange={(e) => updateQuestionField(row.id, 'answerText', e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 w-full"
                    />
                ) : (
                    <span className="block w-full">{value ?? '–'}</span>
                ),
        },
        {
            key: 'summary',
            label: t('summary_text'),
            align: 'center',
            clampLines: 2,   // ⬅️ קיצור סיכום
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="text"
                        value={row.summary ?? ''}
                        onChange={(e) => updateQuestionField(row.id, 'summary', e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 w-full"
                    />
                ) : (
                    <span className="block w-full">{value ?? '–'}</span>
                ),
        },
        {
            key: 'buttonYesText',
            label: t('yes_button'),
            align: 'center',
            clampLines: 1,
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="text"
                        value={row.buttonYesText ?? ''}
                        onChange={(e) => updateQuestionField(row.id, 'buttonYesText', e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 w-full"
                    />
                ) : (
                    <span className="block w-full">{value ?? '–'}</span>
                ),
        },
        {
            key: 'buttonNoText',
            label: t('no_button'),
            align: 'center',
            clampLines: 1,
            render: (value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="text"
                        value={row.buttonNoText ?? ''}
                        onChange={(e) => updateQuestionField(row.id, 'buttonNoText', e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 w-full"
                    />
                ) : (
                    <span className="block w-full">{value ?? '–'}</span>
                ),
        },
        {
            key: 'isQuestion',
            label: t('is_question'),
            align: 'center',
            width: 140,
            noClamp: true, // ⬅️ בלי קיצור באייקונים/טוגלים
            render: (_value, row) =>
                editingRowId === row.id ? (
                    <input
                        type="checkbox"
                        checked={!!row.isQuestion}
                        onChange={(e) => updateQuestionField(row.id, 'isQuestion', e.target.checked)}
                    />
                ) : (
                    <span className="text-center">{row.isQuestion ? t('yes') : t('no')}</span>
                ),
        },
        {
            key: 'actions',
            label: t('actions'),
            align: 'center',
            width: 200,
            noClamp: true, // ⬅️ אין קיצור באקשנס
            render: (_value, row) =>
                editingRowId === row.id ? (
                    <div className="flex items-center justify-center gap-2">
                        <button
                            type="button"
                            onClick={() => handleSaveQuestion(row.id)}
                            className="bg-teal-600 text-white px-2 py-1 rounded hover:bg-teal-500"
                        >
                            {t('save')}
                        </button>
                        <button
                            type="button"
                            onClick={() => setEditingRowId(null)}
                            className="bg-red-600 text-white px-2 py-1 rounded hover:bg-red-500"
                        >
                            {t('cancel')}
                        </button>
                    </div>
                ) : (
                    <div className="flex items-center justify-center gap-2">
                        <button
                            type="button"
                            onClick={() => setEditingRowId(row.id)}
                            className="bg-teal-600 text-white px-2 py-1 rounded hover:bg-teal-500"
                        >
                            {t('edit')}
                        </button>
                    </div>
                ),
        },
    ];

    if (isLoading) return <ProgressBarWidget />;

    return (
        <>
            {/* Add Question Modal */}
            {showForm && (
                <div
                    className="fixed inset-0 bg-gray-900 bg-opacity-50 flex justify-center items-center z-30"
                    dir={i18n.language === 'ar' || i18n.language === 'he' ? 'rtl' : 'ltr'}
                >
                    <div className="w-full max-w-3xl mx-auto px-4">
                        <form
                            onSubmit={handleSubmit}
                            className="p-6 bg-white rounded-lg shadow-lg shadow-[#51B4E2] max-h-[90vh] overflow-y-auto"
                        >
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl md:text-2xl font-semibold">
                                    {t('add_new_question')}
                                </h2>
                                <button
                                    type="button"
                                    onClick={() => setShowForm(false)}
                                    className="text-white bg-red-600 rounded-full p-2 hover:bg-red-700 transition"
                                >
                                    <MdClose size={20} />
                                </button>
                            </div>

                            {formData.questions.map((q, index) => (
                                <div key={index} className="border border-gray-300 rounded-lg p-4 mb-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {/* questionText */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('question_text')}
                                            </label>
                                            <input
                                                type="text"
                                                name="questionText"
                                                value={q.questionText}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
                                                required
                                            />
                                        </div>

                                        {/* questionType */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('question_type')}
                                            </label>
                                            <select
                                                name="questionType"
                                                value={q.questionType}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            >
                                                {typeOptions.map((opt) => (
                                                    <option key={opt.value} value={opt.value}>
                                                        {opt.label}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        {/* answerText */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('answer_text')}
                                            </label>
                                            <input
                                                type="text"
                                                name="answerText"
                                                value={q.answerText}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            />
                                        </div>

                                        {/* summary */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('summary_text')}
                                            </label>
                                            <input
                                                type="text"
                                                name="summary"
                                                value={q.summary}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            />
                                        </div>

                                        {/* buttonYesText */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('yes_button')}
                                            </label>
                                            <input
                                                type="text"
                                                name="buttonYesText"
                                                value={q.buttonYesText}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            />
                                        </div>

                                        {/* buttonNoText */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('no_button')}
                                            </label>
                                            <input
                                                type="text"
                                                name="buttonNoText"
                                                value={q.buttonNoText}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            />
                                        </div>

                                        {/* yes/no routes */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('yes_route')}
                                            </label>
                                            <input
                                                type="number"
                                                name="yesRouteId"
                                                value={q.yesRouteId ?? ''}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {t('no_route')}
                                            </label>
                                            <input
                                                type="number"
                                                name="noRouteId"
                                                value={q.noRouteId ?? ''}
                                                onChange={(e) => handleInputChange(e, index)}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                            />
                                        </div>

                                        {/* isQuestion */}
                                        <div className="flex items-center gap-2">
                                            <input
                                                type="checkbox"
                                                name="isQuestion"
                                                checked={q.isQuestion}
                                                onChange={(e) => handleInputChange(e, index)}
                                            />
                                            <span className="text-sm font-medium">{t('is_question')}</span>
                                        </div>
                                    </div>

                                    {index > 0 && (
                                        <div className="flex justify-end mt-4">
                                            <button
                                                type="button"
                                                onClick={() => handleRemoveQuestionField(index)}
                                                className="text-red-600 bg-gray-200 rounded-md px-4 py-2 hover:bg-red-300 transition"
                                            >
                                                <CgTrash size={20} />
                                            </button>
                                        </div>
                                    )}
                                </div>
                            ))}

                            <div className="mt-6">
                                <button
                                    type="submit"
                                    className="w-full px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-500 transition"
                                >
                                    {t('submit')}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            <TableWidget
                title={t('questions')}
                columns={columns}
                rows={questions}
                isAddClick
                onAddClick={() => setShowForm(true)}
                pageSize={10}
                isMobile={window.innerWidth < 768}
                px={16}
                vh={80}
                noDataMessage="no_questions_available"  // ⬅️ key, לא מחרוזת מתורגמת
                scrollX                          // ⬅️ לאפשר גלילה אופקית כשצריך
                stickyFirstCol
            />
        </>
    );
};

export default Questions;