import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { CgClose, CgTrash } from "react-icons/cg";
import { useNavigate } from "react-router-dom";
import { FaEdit } from "react-icons/fa";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";
import DeleteConfirmationModal from "../../widgets/DeleteModal";

const Meditation = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [meditations, setMeditations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // image lightbox
  const [imageModal, setImageModal] = useState({ isOpen: false, image: null });
  const closeImageModal = () => setImageModal({ isOpen: false, image: null });
  // delete modal
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedMeditation, setSelectedMeditation] = useState(null);

  // create/edit modal
  const [openModal, setOpenModal] = useState(false);
  const [formState, setFormState] = useState({
    id: null,
    title: "",
    description: "",
    image: null,
    secondry_image: null,
  });

  /* ----------------------- data ----------------------- */
  const fetchMeditations = async () => {
    try {
      const { data } = await axiosInstance.get("/meditation");
      setMeditations(Array.isArray(data?.data) ? data.data : []);
    } catch (error) {
      console.error("Error fetching meditations:", error?.message || error);
      setMeditations([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMeditations();
  }, []);

  /* ----------------------- ui helpers ----------------------- */
  const handleImageClick = (imagePath) => {
    if (!imagePath) return;
    const src = /^https?:\/\//i.test(imagePath) ? imagePath : baseURL + imagePath;
    setImageModal({ isOpen: true, image: src });
  };

  /* ----------------------- form handlers ----------------------- */
  const handleInputChange = (e) => {
    const { name, value, files } = e.target;

    if (name === "file") {
      const file = files?.[0] || null;
      setFormState((s) => ({ ...s, image: file }));
      return;
    }
    if (name === "secondry_image") {            // <-- align to router
      const file = files?.[0] || null;
      setFormState((s) => ({ ...s, secondry_image: file }));
      return;
    }

    setFormState((s) => ({ ...s, [name]: value }));
  };

  const handleOpenModal = () => {
    setFormState({ id: null, title: "", description: "", image: null, secondry_image: null });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setFormState({ id: null, title: "", description: "", image: null, secondry_image: null });
  };



  const handleEdit = (item) => {
    setFormState({
      id: item.id,
      title: item.title || "",
      description: item.description || "",
      image: item.image || null,
      secondry_image: item.secondry_image || null,
    });
    setOpenModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formState.title.trim()) {
      alert(t("Please_enter_a_title"));
      return;
    }

    try {
      const fd = new FormData();
      fd.append("title", formState.title);
      fd.append("description", formState.description || "");

      if (formState.image instanceof File) {
        fd.append("image", formState.image);
      }
      if (formState.secondry_image instanceof File) {
        fd.append("secondry_image", formState.secondry_image);
      }

      const isEdit = !!formState.id;
      const url = isEdit ? `/meditation/update/${formState.id}` : "/meditation";
      const method = isEdit ? "put" : "post";

      await axiosInstance[method](url, fd, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      await fetchMeditations();
      handleCloseModal();
    } catch (error) {
      console.error("Error saving meditation:", error?.response?.data || error?.message || error);
    }
  };

  /* ----------------------- delete ----------------------- */
  const openDelete = (med) => {
    setSelectedMeditation(med);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!selectedMeditation) return;
    try {
      await axiosInstance.delete(`/meditation/delete/${selectedMeditation.id}`);
      setIsDeleteModalOpen(false);
      setSelectedMeditation(null);
      fetchMeditations();
    } catch (error) {
      console.error("Error deleting meditation:", error?.response?.data || error?.message || error);
    }
  };

  /* ----------------------- table columns ----------------------- */
  const columns = [
    {
      key: "index",
      label: t("id"),
      align: "center",
      minWidth: 70,
      render: (_v, _r, i) => i + 1,
    },
    {
      key: "image",
      label: t("image"),
      align: "center",
      minWidth: 120,
      render: (_v, row) => (
        <button
          type="button"
          className="w-full h-full flex items-center justify-center"
          onClick={() => handleImageClick(row.image)}
        >
          {row.image ? (
            <img src={baseURL + row.image} className="w-20 h-20 object-cover rounded-md" />
          ) : (
            <span className="text-gray-400 italic">{t("no_image")}</span>
          )}
        </button>
      ),
    },
    {
      key: "secondry_image",
      label: t("secondry_image"),
      align: "center",
      minWidth: 120,
      render: (_v, row) => (
        <button
          type="button"
          className="w-full h-full flex items-center justify-center"
          onClick={() => handleImageClick(row.secondry_image)}
        >
          {row.secondry_image ? (
            <img src={baseURL + row.secondry_image} className="w-20 h-20 object-cover rounded-md" />
          ) : (
            <span className="text-gray-400 italic">{t("no_image")}</span>
          )}
        </button>
      ),
    },
    {
      key: "title",
      label: t("title"),
      align: "center",
      minWidth: 220,
      render: (val, row) => (
        <div
          className="cursor-pointer text-center font-semibold"
          onClick={() => navigate(`/dashboard/meditation/audio/${row.id}`)}
        >
          {val || "—"}
        </div>
      ),
    },
    {
      key: "description",
      label: t("description"),
      align: "center",
      minWidth: 300,
      render: (val, row) => (
        <div
          className="cursor-pointer text-center"
          onClick={() => navigate(`/dashboard/meditation/audio/${row.id}`)}
        >
          {val || "—"}
        </div>
      ),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 160,
      render: (_v, row) => (
        <div className="flex justify-center items-center gap-3">
          <button onClick={() => handleEdit(row)} className="bg-teal-600 text-white p-2 rounded-md">
            <FaEdit size={18} />
          </button>
          <button onClick={() => openDelete(row)} className="bg-red-600 text-white p-2 rounded-md">
            <CgTrash size={18} />
          </button>
        </div>
      ),
    },
  ];


  useEffect(() => {
    // nothing to do if no modal is open
    const anyModalOpen = imageModal.isOpen || openModal;
    if (!anyModalOpen) return;

    // single Escape handler for both modals
    const onKey = (e) => {
      if (e.key !== "Escape") return;
      if (imageModal.isOpen) {
        closeImageModal();
      } else if (openModal) {
        handleCloseModal();
      }
    };

    document.addEventListener("keydown", onKey);

    // lock scroll once; restore exactly what it was
    const prevOverflow = document.body.style.overflow;
    document.body.style.overflow = "hidden";

    return () => {
      document.removeEventListener("keydown", onKey);
      document.body.style.overflow = prevOverflow;
    };
  }, [imageModal.isOpen, openModal]);




  if (isLoading) return <ProgressBarWidget />;


  console.log("meditations", meditations);
  return (
    <>
      <TableWidget
        title={t("meditation")}
        columns={columns}
        rows={meditations}
        onAddClick={handleOpenModal}
        isAddClick
        noDataMessage={t("no_data_available")}
      />

      {imageModal.isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
          onClick={closeImageModal} // click outside to close
        >
          <div
            className="relative max-w-[90vw] max-h-[90vh] bg-white rounded-lg shadow-lg p-2"
            onClick={(e) => e.stopPropagation()} // don't close when clicking the image/card
          >
            <button
              className="absolute top-3 right-3 text-white bg-red-600 rounded-full p-1 shadow"
              onClick={closeImageModal}
              aria-label="Close"
              type="button"
            >
              <CgClose size={22} />
            </button>

            {imageModal.image ? (
              <img
                src={imageModal.image}        // already absolute; don't add baseURL again
                alt="Preview"
                className="object-contain w-[80vw] h-[80vh] max-w-[900px] max-h-[900px] rounded"
                loading="eager"
              />
            ) : (
              <div className="p-8 text-center text-gray-500">{t("no_image")}</div>
            )}
          </div>
        </div>
      )}

      {/* Create/Edit Modal */}
      {openModal && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/60"
          onClick={handleCloseModal} // close when clicking backdrop
        >
          <div
            className="relative w-full max-w-lg mx-4 rounded-2xl bg-white shadow-xl"
            onClick={(e) => e.stopPropagation()} // prevent closing when clicking inside
          >
            {/* Header */}
            <div className="flex items-center justify-between px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">
                {formState.id ? t("edit_meditation") : t("add_meditation")}
              </h2>
              <button
                type="button"
                onClick={handleCloseModal}
                className="inline-flex items-center justify-center rounded-full p-1.5 text-white bg-red-600 hover:bg-red-500"
                aria-label={t("close")}
                title={t("close")}
              >
                <CgClose size={18} />
              </button>
            </div>

            {/* Body */}
            <form onSubmit={handleSubmit} className="px-6 py-5 space-y-5">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t("title")}
                </label>
                <input
                  type="text"
                  name="title"
                  value={formState.title}
                  onChange={handleInputChange}
                  placeholder={t("enter_title_meditation")}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t("description")}
                </label>
                <textarea
                  name="description"
                  value={formState.description}
                  onChange={handleInputChange}
                  placeholder={t("enter_description")}
                  rows={3}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 resize-y focus:outline-none focus:ring-2 focus:ring-teal-500"
                />
              </div>

              {/* Images */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Primary image */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {t("image")} <span className="text-gray-400">({t("optional")})</span>
                  </label>

                  {/* Preview (new file or existing) */}
                  <div className="flex items-center justify-center border border-dashed border-gray-300 rounded-lg p-2 min-h-[112px] bg-gray-50">
                    {formState.image instanceof File ? (
                      <img
                        src={URL.createObjectURL(formState.image)}
                        alt="preview"
                        className="h-24 w-24 object-cover rounded"
                      />
                    ) : formState.id && typeof formState.image === "string" ? (
                      <img
                        src={baseURL + formState.image}
                        alt="current"
                        className="h-24 w-24 object-cover rounded"
                      />
                    ) : (
                      <span className="text-gray-400 text-sm">{t("no_image")}</span>
                    )}
                  </div>

                  <label className="block">
                    <span className="sr-only">Choose image</span>
                    <input
                      type="file"
                      name="file"
                      accept="image/*"
                      onChange={handleInputChange}
                      className="block w-full text-sm text-gray-900 file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-teal-600 file:text-white hover:file:bg-teal-500"
                    />
                  </label>
                </div>

                {/* Secondary image */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {t("secondry_image")} <span className="text-gray-400">({t("optional")})</span>
                  </label>

                  <div className="flex items-center justify-center border border-dashed border-gray-300 rounded-lg p-2 min-h-[112px] bg-gray-50">
                    {formState.secondry_image instanceof File ? (
                      <img
                        src={URL.createObjectURL(formState.secondry_image)}
                        alt="preview-secondary"
                        className="h-24 w-24 object-cover rounded"
                      />
                    ) : formState.id && typeof formState.secondry_image === "string" ? (
                      <img
                        src={baseURL + formState.secondry_image}
                        alt="current-secondary"
                        className="h-24 w-24 object-cover rounded"
                      />
                    ) : (
                      <span className="text-gray-400 text-sm">{t("no_image")}</span>
                    )}
                  </div>

                  <label className="block">
                    <span className="sr-only">Choose image</span>
                    <input
                      type="file"
                      name="secondry_image"
                      accept="image/*"
                      onChange={handleInputChange}
                      className="block w-full text-sm text-gray-900 file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-teal-600 file:text-white hover:file:bg-teal-500"
                    />
                  </label>
                </div>
              </div>

              {/* Footer actions */}
              <div className="flex items-center justify-end gap-3 pt-2">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  {t("cancel")}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded-lg bg-teal-700 text-white hover:bg-teal-600"
                >
                  {t("save")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title={t("confirm_deletion")}
        subTitle={t("are_you_sure_you_want_to_delete_this_meditation")}
      />
    </>
  );
};

export default Meditation;