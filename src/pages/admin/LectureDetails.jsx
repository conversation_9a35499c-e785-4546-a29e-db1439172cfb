/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { useParams } from "react-router-dom";
import toast from "react-hot-toast";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";
import { FaEdit } from "react-icons/fa";
import { MdPlayCircleOutline } from "react-icons/md";
import { CgClose, CgTrash } from "react-icons/cg";
import UploadProgress from "../../widgets/UploadProgress";

/** Normalize a possibly-relative URL to an absolute URL */
const toUrl = (u) => {
  if (!u) return "";
  return /^https?:\/\//i.test(u) ? u : baseURL + u;
};

const LectureDetails = () => {
  const { id } = useParams(); // parent lecture id
  const { t } = useTranslation();

  const [subLectures, setSubLectures] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const [openModal, setOpenModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingLectureId, setEditingLectureId] = useState(null);
  const [editingLectureVideoUrl, setEditingLectureVideoUrl] = useState(null);

  const [formState, setFormState] = useState({
    text: "",
    type: "sub_category",
    description: "",
    image: null, // File when user selects a new video
  });

  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadTime, setUploadTime] = useState(0);
  const [uploadTimer, setUploadTimer] = useState(null);
  const [uploadStage, setUploadStage] = useState(null); // null | 'uploading' | 'refreshing'

  const [selectedVideo, setSelectedVideo] = useState(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  /* ---------------- fetch ---------------- */
  useEffect(() => {
    const tmr = setTimeout(fetchSubLectures, 3000); // keep your splash delay
    return () => clearTimeout(tmr);
  }, []);

  const fetchSubLectures = async () => {
    try {
      const { data } = await axiosInstance.get(`/lectures/lecture-sub-video/${id}`);
      setSubLectures(data.data || []);
    } catch (error) {
      console.error("Failed to fetch sub-lectures:", error);
      toast.error(t("Failed to fetch sub-lectures. Please try again."));
    } finally {
      setIsLoading(false);
    }
  };

  /* ---------------- submit (create or update) ---------------- */
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formState.text?.trim()) {
      toast.error(t("title_required"));
      return;
    }
    if (!isEditing && !formState.image) {
      toast.error(t("please_select_video_file"));
      return;
    }

    const formData = new FormData();
    formData.append("text", formState.text);
    formData.append("description", formState.description || "");
    formData.append("type", formState.type || "sub_category");
    if (formState.image) formData.append("video", formState.image);

    // start upload progress/timer
    setUploadStage("uploading");
    setUploadProgress(0);
    setUploadTime(0);
    if (uploadTimer) clearInterval(uploadTimer);
    const timer = setInterval(() => setUploadTime((s) => s + 1), 1000);
    setUploadTimer(timer);

    const url = isEditing
      ? `/lectures/update-sub/${editingLectureId}`
      : `/lectures/create-sub-video/${id}`;
    const method = isEditing ? "PUT" : "POST";

    try {
      const response = await axiosInstance({
        method,
        url,
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        timeout: 6 * 60 * 1000,
        onUploadProgress: (pe) => {
          if (pe.total) {
            setUploadProgress(Math.round((pe.loaded * 100) / pe.total));
          }
        },
      });

      console.log("Upload response:", response);

      toast.success(
        isEditing ? t("lecture_updated_successfully") : t("lecture_added_successfully")
      );

      setUploadStage("refreshing");
      await fetchSubLectures();
      resetForm();
    } catch (error) {
      console.error("Upload failed:", error);
      const msg = error?.response?.data?.error || error.message;
      toast.error(t("upload_failed") + ": " + msg);
    } finally {
      setUploadStage(null);
      if (uploadTimer) {
        clearInterval(uploadTimer);
        setUploadTimer(null);
      }
    }
  };

  /* ---------------- edit ---------------- */
  const handleEditLecture = async (lecture) => {
    try {
      const { data } = await axiosInstance.get(`/lectures/${lecture.id}`);
      const rec = data.data || lecture;

      setFormState({
        text: rec.text || "",
        description: rec.description || "",
        type: rec.type || "sub_category",
        image: null, // user hasn't picked a new file yet
      });
      setEditingLectureId(lecture.id);
      setEditingLectureVideoUrl(rec.image ? toUrl(rec.image) : null);

      setIsEditing(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Failed to load lecture for editing:", error);
      toast.error(t("Failed to edit lecture. Please try again."));
    }
  };

  /* ---------------- delete ---------------- */
  const handleDeleteLecture = async (subId) => {
    try {
      await axiosInstance.delete(`/lectures/delete-sub/${subId}`);
      toast.success(t("lecture_deleted_successfully"));
      fetchSubLectures();
    } catch (error) {
      console.error("Failed to delete lecture:", error);
      toast.error(t("Failed to delete lecture. Please try again."));
    }
  };

  /* ---------------- reset modal ---------------- */
  const resetForm = () => {
    setOpenModal(false);
    setIsEditing(false);
    setEditingLectureId(null);
    setEditingLectureVideoUrl(null);
    setFormState({
      text: "",
      type: "sub_category",
      description: "",
      image: null,
    });
    setUploadProgress(0);
    setUploadStage(null);
  };

  /* ---------------- video modal ---------------- */
  const handlePlayVideo = (videoPathOrUrl) => {
    setSelectedVideo(toUrl(videoPathOrUrl));
    setIsVideoModalOpen(true);
  };

  /* ---------------- loading ---------------- */
  if (isLoading) return <ProgressBarWidget />;

  /* ---------------- columns (render(value, row, index)) ---------------- */
  const columns = [
    {
      key: "id",
      label: t("id"),
      align: "center",
      minWidth: 80,
      render: (_value, _row, index) => index + 1,
    },
    {
      key: "image",
      label: t("video_lecture"),
      align: "center",
      minWidth: 160,

      // IMPORTANT: render(value, row) — value is the cell value for key "image"
      render: (value, _row ) => {
        return (
          <div
            className="relative w-full h-32 flex items-center justify-center rounded-md cursor-pointer group"
            onClick={() => handlePlayVideo(value)}
            // title={t("play")}
          >
            <video
              src={baseURL + value}
              className="w-32 h-full rounded-md bg-gray-200 object-cover"
              preload="metadata"
              controls
            />
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition">
              <MdPlayCircleOutline size={36} />
            </div>
          </div>
        );
      },
    },
    {
      key: "text",
      label: t("title"),
      align: "center",
      minWidth: 220,
      render: (value) => (
        <div
          className="max-w-[300px] mx-auto text-center whitespace-nowrap overflow-hidden text-ellipsis"
          title={value || ""}
        >
          {value || "—"}
        </div>
      ),
    },
    {
      key: "description",
      label: t("description"),
      align: "center",
      minWidth: 320,
      render: (value) => (
        <div
          className="max-w-[420px] mx-auto text-center whitespace-nowrap overflow-hidden text-ellipsis"
          title={value || ""}
        >
          {value || "—"}
        </div>
      ),
    },
    {
      key: "type",
      label: t("type"),
      align: "center",
      minWidth: 140,
      render: (value) =>
        value === "sub_category" ? t("sub_category") : t("category"),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 180,
      render: (_value, row) => (
        <div className="flex gap-2 items-center justify-center">
          <button
            className="bg-teal-700 hover:bg-teal-500 text-white p-2 rounded-lg"
            onClick={() => handleEditLecture(row)}
            title={t("edit")}
          >
            <FaEdit />
          </button>
          <button
            className="bg-red-700 hover:bg-red-500 text-white p-2 rounded-lg"
            onClick={() => handleDeleteLecture(row.id)}
            title={t("delete")}
          >
            <CgTrash />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <TableWidget
        columns={columns}
        rows={subLectures}
        title={t("lectures_details")}
        noDataMessage={t("no_data_available")}
        onAddClick={() => {
          resetForm();        // make sure we're not editing
          setOpenModal(true); // open in create mode
        }}
        vh={90}
        scrollX
        minTableWidth={1100}
        clampLines={1}
        ellipsisTooltip
      />

      {/* Create/Edit Modal */}
      {openModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-[600px]">
            <div className="relative flex items-center mb-4">
              <button
                className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full"
                onClick={resetForm}
                type="button"
              >
                <CgClose />
              </button>
              <h2 className="text-lg font-semibold w-full text-center">
                {isEditing ? t("edit_lecture") : t("add_lecture")}
              </h2>
            </div>

            <form
              onSubmit={handleSubmit}
              encType="multipart/form-data"
              className="flex flex-col gap-4"
            >
              <label className="font-medium text-gray-700">{t("title")}</label>
              <input
                type="text"
                value={formState.text}
                onChange={(e) =>
                  setFormState((s) => ({ ...s, text: e.target.value }))
                }
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />

              <label className="font-medium text-gray-700">
                {t("description")}
              </label>
              <input
                type="text"
                value={formState.description}
                onChange={(e) =>
                  setFormState((s) => ({ ...s, description: e.target.value }))
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />

              <label className="font-medium text-gray-700">{t("video")}</label>
              <input
                type="file"
                accept="video/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    setFormState((s) => ({ ...s, image: file }));
                  }
                }}
                className="w-full p-2 border border-gray-300 rounded-md"
              />

              {isEditing && (
                <>
                  {!formState.image && editingLectureVideoUrl && (
                    <video
                      src={editingLectureVideoUrl}
                      controls
                      className="mt-2 w-full max-h-40 rounded-md object-contain"
                    />
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    {t("leave_empty_to_keep_current_video")}
                  </p>
                </>
              )}

              <button
                type="submit"
                disabled={uploadStage !== null}
                className="w-full bg-blue-600 hover:bg-blue-500 text-white py-2 rounded-md flex items-center justify-center"
              >
                {uploadStage === "uploading" ? (
                  <>
                    <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    {t("uploading_video")}... {uploadProgress}%
                  </>
                ) : uploadStage === "refreshing" ? (
                  <>
                    <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    {t("refreshing_table")}...
                  </>
                ) : (
                  <>{isEditing ? t("update") : t("save")}</>
                )}
              </button>
            </form>

            {uploadStage === "uploading" && (
              <UploadProgress
                progress={uploadProgress}
                time={uploadTime}
                label={t("uploading")}
              />
            )}
          </div>
        </div>
      )}

      {/* Playback Modal */}
      {isVideoModalOpen && selectedVideo && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-70 z-50">
          <div className="rounded-lg bg-white p-10 max-w-4xl w-full relative">
            <button
              className="absolute top-3 right-3 p-1 text-white bg-red-500 hover:bg-red-600 rounded-full cursor-pointer"
              onClick={() => {
                setIsVideoModalOpen(false);
                setSelectedVideo(null);
              }}
            >
              <CgClose size={20} />
            </button>
            <video controls src={selectedVideo} className="w-full rounded-lg" />
          </div>
        </div>
      )}
    </>
  );
};

export default LectureDetails;