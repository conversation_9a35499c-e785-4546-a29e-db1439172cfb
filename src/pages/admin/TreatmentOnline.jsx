/* eslint-disable react-hooks/exhaustive-deps */
import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { FaEdit } from "react-icons/fa";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { CgClose } from "react-icons/cg";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";
import "react-quill/dist/quill.snow.css";
import ReactQuill from "react-quill";

const OnlineTreatment = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);

  const [modalOpen, setModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentVideoId, setCurrentVideoId] = useState(null);

  const [formData, setFormData] = useState({
    videoTitle: "",
    videoDescription: "",
    category: "category",
    price: 0,
    file: null,
  });

  // --- helpers ---
  const resolveUrl = (v) => {
    if (!v) return null;
    return /^https?:\/\//i.test(v) ? v : `${baseURL}${v}`;
  };

  const fetchVideos = async () => {
    try {
      const { data } = await axiosInstance.get("/videos");
      setVideos(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching videos:", error);
      toast.error(t("Failed to fetch videos"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos();
  }, []);

  // --- modal open/close ---
  const handleOpenModal = (video = null) => {
    setIsEditing(!!video);
    setCurrentVideoId(video?.id ?? null);
    setFormData({
      videoTitle: video?.text || "",
      videoDescription: video?.description || "",
      category: video?.type || "category",
      price: Number(video?.price ?? 0),
      file: null,
    });
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setIsEditing(false);
    setCurrentVideoId(null);
    setFormData({
      videoTitle: "",
      videoDescription: "",
      category: "category",
      price: 0,
      file: null,
    });
  };

  // --- form handlers ---
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((p) => ({ ...p, [name]: name === "price" ? Number(value) : value }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files?.[0] ?? null;
    setFormData((p) => ({ ...p, file }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const { videoTitle, videoDescription, category, file, price } = formData;

    if (!videoTitle || !category || (!file && !isEditing) || price === null) {
      toast.error(t("All fields are required"));
      return;
    }

    const submitData = new FormData();
    submitData.append("text", videoTitle);
    submitData.append("description", videoDescription || "");
    submitData.append("price", String(price ?? 0));
    submitData.append("category", category);
    if (file) submitData.append("image", file);

    try {
      const endpoint = isEditing ? `/videos/update/${currentVideoId}` : "/videos/create";
      const method = isEditing ? axiosInstance.put : axiosInstance.post;

      await method(endpoint, submitData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      toast.success(t(isEditing ? "Video updated successfully" : "Video added successfully"));
      fetchVideos();
      handleCloseModal();
    } catch (error) {
      const errMsg =
        error?.response?.data?.error || error.message || t("An unexpected error occurred");
      toast.error(errMsg);
    }
  };

  // toggle active/delete (kept as your logic)
  const handleToggle = async (id, currentStatus) => {
    try {
      const endpoint = currentStatus === 1 ? `/videos/delete/${id}` : `/videos/is-active/${id}`;
      await axiosInstance.put(endpoint);
      fetchVideos();
    } catch (error) {
      console.error("Error toggling video status:", error?.response?.data || error.message);
      toast.error(t("Failed to toggle video status"));
    }
  };

  const handleNavigateToVideo = (id) => {
    navigate(`/dashboard/online-treatment/treatment-video/${id}`);
  };

  // --- Quill toolbar ---
  const quillModules = {
    toolbar: [
      [{ font: [] }, { size: [] }],
      [{ header: [1, 2, 3, 4, 5, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ color: [] }, { background: [] }],
      [{ script: "sub" }, { script: "super" }],
      [{ align: [] }],
      [{ list: "ordered" }, { list: "bullet" }],
      ["blockquote", "code-block"],
      ["link", "image", "video"],
      ["clean"],
    ],
  };

  const quillFormats = [
    "header",
    "font",
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "color",
    "background",
    "script",
    "align",
    "list",
    "bullet",
    "blockquote",
    "code-block",
    "link",
    "image",
    "video",
  ];

  // ---------- Table columns ----------
  const columns = [
    { key: "id", label: t("id"), align: "center", minWidth: 80 },

    {
      key: "image",
      label: t("image"),
      align: "center",
      minWidth: 180,
      render: (value, row) => {
        const src = resolveUrl(value);
        return (
          <div
            className="flex items-center justify-center cursor-pointer"
            onClick={() => handleNavigateToVideo(row.id)}
            title={t("open")}
          >
            {src ? (
              <img
                src={src}
                alt="preview"
                className="h-[100px] w-[160px] object-cover rounded border"
              />
            ) : (
              <div className="h-[100px] w-[160px] rounded border flex items-center justify-center text-xs text-gray-400">
                {t("no_image")}
              </div>
            )}
          </div>
        );
      },
    },

    {
      key: "text",
      label: t("title"),
      align: "center",
      minWidth: 220,
      render: (value, row) => (
        <div
          className="cursor-pointer max-w-[380px] mx-auto line-clamp-2"
          onClick={() => handleNavigateToVideo(row.id)}
          title={value}
        >
          {value}
        </div>
      ),
    },

    {
      key: "description",
      label: t("description"),
      align: "center",
      minWidth: 420,
      render: (value, row) => (
        <div
          className="mx-auto max-w-[520px] text-right text-xs overflow-y-auto max-h-[100px] cursor-pointer prose prose-sm"
          onClick={() => handleNavigateToVideo(row.id)}
          // You are storing HTML; keep this if you trust your source
          dangerouslySetInnerHTML={{ __html: value || "" }}
          title={t("open")}
        />
      ),
    },

    {
      key: "price",
      label: t("price"),
      align: "center",
      minWidth: 120,
      render: (value /*, row*/) => (
        <p className="flex items-center justify-center w-full h-full">
          {value}{value ? " $" : ""}
        </p>
      ),
    },

    {
      key: "type",
      label: t("type"),
      align: "center",
      minWidth: 160,
      render: (value /*, row*/) => t(value === "category" ? "category" : "sub_category"),
    },

    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 200,
      render: (_value, row) => (
        <div className="flex items-center gap-3 justify-center">
          <button
            className="bg-teal-600 text-white p-2 rounded hover:bg-teal-500"
            onClick={() => handleOpenModal(row)}
            title={t("edit")}
          >
            <FaEdit size={18} />
          </button>

          <div
            className={`relative w-12 h-6 rounded-full cursor-pointer transition-colors ${
              row.is_active ? "bg-teal-500" : "bg-red-300"
            }`}
            onClick={() => handleToggle(row.id, row.is_active)}
            title={row.is_active ? t("active") : t("inactive")}
          >
            <span
              className={`absolute top-1 left-0 h-4 w-4 rounded-full bg-white shadow transform transition-transform ${
                row.is_active ? "translate-x-6" : "translate-x-1"
              }`}
            />
          </div>
        </div>
      ),
    },
  ];

  if (loading) return <ProgressBarWidget />;

  return (
    <>
      <TableWidget
        title={t("video_treatment")}
        columns={columns}
        rows={videos}
        vh={90}
        isAddClick
        onAddClick={() => handleOpenModal()}
        noDataMessage="no_data_available"
        // Optional scrolling/paging if your TableWidget supports:
        // scrollY="70vh"
        // scrollX
        // minTableWidth={1100}
      />

      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-6 rounded w-full max-w-3xl">
            <div className="flex justify-between mb-4">
              <h2 className="text-xl font-semibold">
                {isEditing ? t("edit_video") : t("add_video")}
              </h2>
              <button onClick={handleCloseModal} className="text-red-600" aria-label={t("close")}>
                <CgClose size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Title */}
              <div>
                <label className="block text-sm mb-1">{t("treatment_title")}</label>
                <input
                  type="text"
                  name="videoTitle"
                  value={formData.videoTitle}
                  onChange={handleInputChange}
                  placeholder={t("enter_treatment_title")}
                  className="w-full border px-3 py-2 rounded"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm mb-1">{t("treatment_description")}</label>
                <ReactQuill
                  value={formData.videoDescription}
                  onChange={(value) =>
                    setFormData((prev) => ({ ...prev, videoDescription: value }))
                  }
                  modules={quillModules}
                  formats={quillFormats}
                />
              </div>

              {/* Price */}
              <div>
                <label className="block text-sm mb-1">{t("treatment_price")}</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="w-full border px-3 py-2 rounded"
                  min={0}
                  required
                />
              </div>

              {/* Image */}
              <div>
                <label className="block text-sm mb-1">{t("upload_image")}</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="block w-full"
                  required={!isEditing}
                />
                {formData.file ? (
                  <img
                    src={URL.createObjectURL(formData.file)}
                    alt="Preview"
                    className="h-32 mt-2 object-contain border rounded"
                  />
                ) : isEditing ? (
                  <img
                    src={resolveUrl(videos.find((v) => v.id === currentVideoId)?.image)}
                    alt="Current"
                    className="h-32 mt-2 object-contain border rounded"
                  />
                ) : null}
              </div>

              <div className="flex justify-end">
                <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded">
                  {isEditing ? t("update") : t("submit")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default OnlineTreatment;