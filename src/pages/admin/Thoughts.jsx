/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axiosInstance, { baseURL } from '../../utils/instance_axios';
import TableWidget from '../../widgets/TableWidget';
import { FaEdit, FaPlus, FaRegFileAudio } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { CgClose, CgTrash } from 'react-icons/cg';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';

const Thoughts = () => {
    const { id } = useParams();
    const [isLoading, setIsLoading] = useState(true);
    const [thoughts, setThoughts] = useState([]);
    const [treatments, setTreatments] = useState([]); // NEW
    const [newThoughts, setNewThoughts] = useState([
        { id: null, content: '', audioFile: null, thoughtId: null, duration: 0, belongsTo: '' }, // NEW prop
    ]);
    const [isAddingThought, setIsAddingThought] = useState(false);
    const [isEditingThought, setIsEditingThought] = useState(false);
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();

    const fetchThoughts = async () => {
        try {
            const { data } = await axiosInstance.get(`/thoughts/${id}`);
            setThoughts(Array.isArray(data) ? data : []);
        } catch (error) {
            console.error('Error fetching thoughts:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // --- fetch treatments (names only) ---
    const fetchTreatments = async () => {
        try {
            // adjust endpoint if yours differs (e.g., /treatments/names)
            const { data } = await axiosInstance.get('/videos');

            const list = Array.isArray(data) ? data.map((x) => ({
                id: x.id ?? x._id ?? x.name ?? x.text,
                text: x.text ?? x.name ?? '', // <-- always have .text
            })) : [];
            setTreatments(list);
        } catch (err) {
            console.error('Error fetching treatments:', err);
            setTreatments([]);
        }
    };



    useEffect(() => {
        fetchThoughts();
        fetchTreatments();
    }, [id]);

    const deleteThought = async (thoughtId) => {
        try {
            const response = await axiosInstance.delete(`/thoughts/${thoughtId}`);
            if (response.status === 200) await fetchThoughts();
            else throw new Error('Failed to delete thought');
        } catch (error) {
            console.error('Error deleting thought:', error);
        }
    };

    const handleEditThought = (thought) => {
        setNewThoughts([
            {
                id: thought.id,
                content: thought.content,
                audioFile: thought.audio_url, // url string kept for edit
                thoughtId: thought.id,
                duration: thought.duration || 0,
                belongsTo: thought.belongs_to || '', // NEW prefill
            },
        ]);
        setIsAddingThought(false);
        setIsEditingThought(true);
    };

    const handleAddInput = async (e) => {
        e.preventDefault();

        const validThoughts = newThoughts.filter((t) => t.content.trim());
        if (validThoughts.length === 0) return;

        try {
            const withDurations = await Promise.all(
                validThoughts.map(async (t) => {
                    if (t.audioFile && typeof t.audioFile !== 'string') {
                        const audio = new Audio(URL.createObjectURL(t.audioFile));
                        const duration = await new Promise((resolve, reject) => {
                            audio.onloadedmetadata = () => resolve(audio.duration || 0);
                            audio.onerror = (err) => reject(new Error(`duration error: ${err?.message || ''}`));
                        });
                        return { ...t, duration };
                    }
                    return { ...t, duration: t.duration || 0 };
                })
            );
            
            await Promise.all(
                withDurations.map(async (t) => {
                    const fd = new FormData();
                    fd.append('content', t.content);
                    fd.append('duration', String(t.duration));
                    fd.append('concern_id', String(id));
                    fd.append('belongs_to', t.belongsTo || '');   // <-- IMPORTANT

                    if (t.audioFile && typeof t.audioFile !== 'string') {
                        fd.append('audioFile', t.audioFile);
                    }
                    if (t.thoughtId) {
                        await axiosInstance.put(`/thoughts/${t.thoughtId}`, fd, {
                            headers: { 'Content-Type': 'multipart/form-data' },
                        });
                    } else {
                        await axiosInstance.post(`/thoughts`, fd, {
                            headers: { 'Content-Type': 'multipart/form-data' },
                        });
                    }
                })
            );

            setNewThoughts([{ id: null, content: '', audioFile: null, thoughtId: null, duration: 0, belongsTo: '' }]);
            setIsAddingThought(false);
            setIsEditingThought(false);
             fetchThoughts();

        } catch (error) {
            console.error('Error handling thoughts:', error);
            alert('Failed to process thoughts. Please try again.');
        }
    };

    const handleThoughtChange = (index, value, field) => {
        setNewThoughts((prev) => {
            const next = [...prev];
            next[index] = { ...next[index], [field]: value };
            return next;
        });
    };

    const addThoughtInput = () => {
        setNewThoughts((p) => [...p, { id: null, content: '', audioFile: null, thoughtId: null, duration: 0, belongsTo: '' }]);
    };

    const removeThoughtInput = (index) => {
        setNewThoughts((p) => p.filter((_, i) => i !== index));
    };

    const resolveUrl = (v) => {
        if (!v) return null;
        return /^https?:\/\//i.test(v) ? v : `${baseURL}${v}`;
        // NOTE: if baseURL already ends with '/', ensure no double slash
    };

    const columns = [
        {
            key: 'id',
            label: t('id'),
            align: 'center',
            flex: 0.5,
            render: (value) => <span className="text-lg font-semibold">{value}</span>,
        },
        {
            key: 'content',
            label: t('content'),
            flex: 1,
            render: (value, { id }) => (
                <div className="flex items-center font-semibold justify-center text-center cursor-pointer hover:underline">
                    <span className="text-lg" onClick={() => navigate(`/dashboard/questions/thoughts/${id}`)}>
                        {value}
                    </span>
                </div>
            ),
        },
        {
            key: 'audio_url',
            label: t('audio'),
            flex: 1,
            render: (value) => {
                const src = value ? resolveUrl(value) : null;
                return src ? (
                    <audio controls src={src} className="h-[48px] w-full px-2">
                        Your browser does not support the audio element.
                    </audio>
                ) : (
                    <span className="text-gray-400">{t('no_audio')}</span>
                );
            },
        },
        {
            key: 'belongs_to',
            label: t('belongs_to'),
            render: (value) => (!value ? '–' : <span className="text-lg font-semibold">{value}</span>),
        },
        {
            key: 'actions',
            label: t('actions'),
            align: 'center',
            render: (_value, row) => {
                const { id } = row;
                return (
                    <div className="flex space-x-2 items-center justify-center w-full gap-2">
                        <button
                            onClick={() => handleEditThought(row)}
                            className="text-white bg-teal-600 hover:bg-teal-700 rounded-md p-2"
                            title={t('edit')}
                        >
                            <FaEdit size={18} />
                        </button>
                        <button
                            onClick={() => deleteThought(id)}
                            className="text-white bg-red-600 hover:bg-red-700 rounded-md p-2"
                            title={t('delete')}
                        >
                            <CgTrash size={18} />
                        </button>
                    </div>
                );
            },
        },
    ];

    if (isLoading) return <ProgressBarWidget />;

    return (
        <>
            {(isAddingThought || isEditingThought) && (
                <div
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
                    role="dialog"
                    aria-modal="true"
                    aria-labelledby="manage-thoughts-title"
                >
                    <div
                        className="relative w-[92%] max-w-3xl rounded-2xl bg-white p-5 shadow-xl ring-1 ring-black/5"
                        dir={i18n.language === 'ar' || i18n.language === 'he' ? 'rtl' : 'ltr'}
                    >
                        {/* Close */}
                        <button
                            type="button"
                            onClick={() => {
                                setIsAddingThought(false);
                                setNewThoughts([{ id: null, content: '', audioFile: null, thoughtId: null, duration: 0, belongsTo: '' }]);
                                setIsEditingThought(false);
                            }}
                            className="absolute top-3 ltr:right-3 rtl:left-3 inline-flex items-center justify-center rounded-full bg-red-600/90 p-2 text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                            aria-label={t('close')}
                            title={t('close')}
                        >
                            <CgClose size={18} />
                        </button>

                        {/* Header */}
                        <div className="mb-4 flex items-center justify-between gap-3">
                            <h1 id="manage-thoughts-title" className="text-2xl font-semibold text-teal-700">
                                {t('manage_thoughts')}
                            </h1>

                            {!isEditingThought && (
                                <button
                                    type="button"
                                    title={t('add')}
                                    onClick={addThoughtInput}
                                    className="inline-flex h-10 w-10 items-center justify-center rounded-full bg-teal-700 text-white transition hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
                                    aria-label={t('add')}
                                >
                                    <FaPlus size={20} />
                                </button>
                            )}
                        </div>

                        {/* Form */}
                        <form onSubmit={handleAddInput} className="mt-2" encType="multipart/form-data">
                            <div className="flex flex-col gap-4">
                                {newThoughts.map((thought, index) => {
                                    const uid = (thought?.id ?? index).toString();
                                    const inputId = `thought-content-${uid}`;
                                    const fileId = `audioFile-${uid}`;

                                    return (
                                        <div key={uid} className="rounded-xl border border-gray-200 p-4 shadow-sm">
                                            <div className="flex flex-col gap-3 md:flex-row md:items-start md:flex-wrap">
                                                {/* Text input */}
                                                <div className="flex-1 min-w-[250px]">
                                                    <label htmlFor={inputId} className="mb-1 block text-sm font-medium text-gray-700">
                                                        {`${index + 1}# ${t('add_thought')}`}
                                                    </label>
                                                    <input
                                                        id={inputId}
                                                        type="text"
                                                        value={thought.content}
                                                        onChange={(e) => handleThoughtChange(index, e.target.value, 'content')}
                                                        className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:border-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500"
                                                        dir={i18n.language === 'ar' || i18n.language === 'he' ? 'rtl' : 'ltr'}
                                                        placeholder={`${index + 1}# ${t('add_thought')}`}
                                                    />
                                                </div>

                                                {/* Belongs To (Treatment Name) */}
                                                <div className="md:w-1/3 min-w-[200px] relative">
                                                    <label className="mb-1 block text-sm font-medium text-gray-700">
                                                        {t('belongs_to')}
                                                    </label>

                                                    {/* Displayed field */}
                                                    <div
                                                        role="button"
                                                        tabIndex={0}
                                                        className="w-full cursor-pointer rounded-lg border border-gray-300 px-4 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-teal-500"
                                                        onClick={() => handleThoughtChange(index, !thought.dropdownOpen, 'dropdownOpen')}
                                                        onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && handleThoughtChange(index, !thought.dropdownOpen, 'dropdownOpen')}
                                                    >
                                                        {thought.belongsTo || t('treatment')}
                                                    </div>

                                                    {/* Dropdown list */}
                                                    {thought.dropdownOpen && (
                                                        <div className="absolute z-10 mt-1 w-full rounded-lg border border-gray-200 bg-white shadow-lg max-h-48 overflow-y-auto">
                                                            {treatments.length > 0 ? (
                                                                treatments.map((tr) => (
                                                                    <div
                                                                        key={tr.id}
                                                                        className="px-4 py-2 cursor-pointer hover:bg-teal-100"
                                                                        onClick={() => {
                                                                            handleThoughtChange(index, tr.text, 'belongsTo');     // <-- save string
                                                                            handleThoughtChange(index, false, 'dropdownOpen');     // close
                                                                        }}
                                                                    >
                                                                        {tr.text}
                                                                    </div>
                                                                ))
                                                            ) : (
                                                                <div className="px-4 py-2 text-gray-400">{t('no_treatments')}</div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>

                                                {/* File input */}
                                                <div className="md:w-1/2 min-w-[250px]">
                                                    <span className="mb-1 block text-sm font-medium text-gray-700">{t('audio')}</span>

                                                    <label
                                                        htmlFor={fileId}
                                                        className="group inline-flex w-full items-center justify-between gap-3 rounded-lg border border-gray-300 bg-teal-700 px-4 py-2 text-white transition hover:bg-teal-600 focus-within:ring-2 focus-within:ring-teal-500"
                                                    >
                                                        <input
                                                            id={fileId}
                                                            type="file"
                                                            accept="audio/*"
                                                            className="sr-only"
                                                            onChange={(e) => handleThoughtChange(index, e.target.files?.[0] || null, 'audioFile')}
                                                        />

                                                        <div className="inline-flex items-center gap-2 rounded-md bg-white px-3 py-1 text-teal-700">
                                                            <FaRegFileAudio size={18} />
                                                            <p className="truncate text-sm">
                                                                {thought.audioFile
                                                                    ? typeof thought.audioFile === 'string'
                                                                        ? t('replace_audio')
                                                                        : (thought.audioFile).name
                                                                    : t('upload_audio')}
                                                            </p>
                                                        </div>

                                                        <span className="text-sm opacity-90">{t('browse')}</span>
                                                    </label>

                                                    {/* Audio preview */}
                                                    {thought.audioFile && (
                                                        <div className="mt-2">
                                                            <audio
                                                                controls
                                                                className="h-10 w-full"
                                                                src={
                                                                    typeof thought.audioFile === 'string'
                                                                        ? baseURL + thought.audioFile
                                                                        : URL.createObjectURL(thought.audioFile)
                                                                }
                                                            />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Remove */}
                                                {index > 0 && (
                                                    <div className="flex md:mt-6 md:w-16 md:justify-end">
                                                        <button
                                                            type="button"
                                                            onClick={() => removeThoughtInput(index)}
                                                            className="inline-flex items-center gap-1 rounded-lg bg-gray-100 px-3 py-2 text-red-600 transition hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                                                            title={t('delete')}
                                                            aria-label={t('delete')}
                                                        >
                                                            <CgTrash size={18} />
                                                            <span className="hidden text-sm md:inline">{t('delete')}</span>
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>

                            {/* Submit */}
                            <div className="mt-5 flex justify-end">
                                <button
                                    type="submit"
                                    className="inline-flex items-center rounded-lg bg-teal-700 px-5 py-2.5 text-white transition hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
                                >
                                    {t('submit_thoughts')}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            <TableWidget
                title={t('thoughts_list')}
                px={18}
                pageSize={12}
                vh={80}
                columns={columns}
                rows={thoughts}
                scrollX={false}
                onAddClick={() => {
                    setIsAddingThought(true);
                    setIsEditingThought(false);
                    setNewThoughts([{ id: null, content: '', audioFile: null, thoughtId: null, duration: 0, belongsTo: '' }]);
                }}
            />
        </>
    );
};

export default Thoughts;