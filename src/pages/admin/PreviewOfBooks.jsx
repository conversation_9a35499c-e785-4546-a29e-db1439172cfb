import { useEffect, useState } from 'react';
import axiosInstance, { baseURL } from '../../utils/instance_axios';
import { toast } from 'react-toastify';
import { useParams } from 'react-router-dom';
import HTMLFlipBook from 'react-pageflip';
import { useTranslation } from 'react-i18next';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';

const BookFlip = () => {
  const { bookId } = useParams();
  const [pages, setPages] = useState([]);
  const [coverImage, setCoverImage] = useState(null);
  const [secondaryImage, setSecondaryImage] = useState(null);
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    const fetchBookPages = async () => {
      try {
        const response = await axiosInstance.get(`/books/${bookId}`);
        const bookData = response.data.data;

        console.log('bookData:', bookData);

        if (!bookData) {
          toast.error('No data available for this book.');
          return;
        }

        setCoverImage(bookData.image || null);
        setSecondaryImage(bookData.image_secondary || null);
        setPages(Array.isArray(bookData.pages) ? bookData.pages.flat() : []);
      } catch (error) {
        console.error('Error fetching book pages:', error);
        toast.error('Failed to fetch book data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    setTimeout(fetchBookPages, 2500); // Load data after 2.5 seconds
  }, [bookId]);

  if (loading) {
    return <ProgressBarWidget />;
  }

  const pagesExist = pages.length > 0;
  const hasValidImages = coverImage || secondaryImage;

  if (!hasValidImages && !pagesExist) {
    return <div className="p-4 text-center">{t('no_book_data')}</div>;
  }

  return (
    <div className="h-[99.5%] w-[100%] flex justify-center items-center p-4" dir="ltr">
      <HTMLFlipBook
        width={600}
        height={800}
        maxShadowOpacity={0.5}
        showCover={true}
        className="flip-book-container"
        drawShadow={true}
        flippingTime={1000}
        useMouseEvents={true}
        showPageCorners={true}
        startPage={pages.length}
      >
        {/* Back Cover */}
        {
          <div className="demoPage flex items-center justify-center rounded-md">
            <img src={`${baseURL}${secondaryImage}`|| ''} alt="Book Cover" className="w-full h-full object-fill" />
          </div>
        }

        {/* Pages */}
        {pagesExist ? (
          pages.slice().reverse().map((page, index) => (
            <div key={index} className="demoPage flex items-center justify-center shadow-lg shadow-gray-700 rounded-md">
              <img src={`${baseURL}${page.page_image}`} alt={`Page ${index + 1}`} className="w-full h-full object-cover rounded-md" />
            </div>
          ))
        ) : (
          <div className="demoPage flex items-center justify-center rounded-md bg-gray-200">
            <p className="text-center text-gray-500">{t('no_book_data')}</p>
          </div>
        )}
        {/* Cover Page */}
        {
          <div className="demoPage flex items-center justify-center rounded-md">
            <img src={`${baseURL}${coverImage}`} alt="Book Cover" className="w-full h-full object-fill" />
          </div>
        }

      </HTMLFlipBook>
    </div>
  );
};

export default BookFlip;
