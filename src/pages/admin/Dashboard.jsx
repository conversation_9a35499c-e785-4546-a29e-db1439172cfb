import { useState, useEffect } from 'react';
import axiosInstance from '../../utils/instance_axios';
import { toast } from 'react-toastify';
import { FaUsers } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';

const Dashboard = () => {
  const [userStats, setUserStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const { t } = useTranslation();

  const fetchUserStats = async () => {
    try {
      
      const response = await axiosInstance.get('/auth');
      const users = response.data.data;

      const stats = {
        totalUsers: users.length,
        activeUsers: users.filter((user) => user.is_active).length,
        inactiveUsers: users.filter((user) => !user.is_active).length,
      };

      setUserStats(stats);
    } catch (error) {
      console.error('Error fetching user stats:', error);
      toast.error('Failed to fetch user statistics');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
      fetchUserStats();
  }, []);

  if (isLoading === true) {
    return <ProgressBarWidget />;
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">{t('dashboard')}</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="p-4 bg-blue-100 rounded-lg shadow">
          <FaUsers size={30} className="text-blue-600" />
          <h2>{t('total_users')}</h2>
          <p>{userStats.totalUsers}</p>
        </div>
        <div className="p-4 bg-green-100 rounded-lg shadow">
          <FaUsers size={30} className="text-green-600" />
          <h2>{t('active_users')}</h2>
          <p>{userStats.activeUsers}</p>
        </div>
        <div className="p-4 bg-red-100 rounded-lg shadow">
          <FaUsers size={30} className="text-red-600" />
          <h2>{t('inactive_users')}</h2>
          <p>{userStats.inactiveUsers}</p>
        </div>
        
      </div>
    </div>
  );
};

export default Dashboard;
