/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { CgClose, CgEye } from "react-icons/cg";
import TableWidget from "../../widgets/TableWidget";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { FaCloudUploadAlt, FaEdit } from "react-icons/fa";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

const Concerns = () => {
  const { name = "" } = useParams();
  const [concerns, setConcerns] = useState([]);
  const [concern, setConcern] = useState({ name: "", image: null });
  const [imagePreview, setImagePreview] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [show, setShow] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingConcernId, setEditingConcernId] = useState(null);
  const { t } = useTranslation();
  const navigate = useNavigate();

  const fetchConcerns = async () => {
    setIsLoading(true);
    try {
      const res = await axiosInstance.get("/concerns");
      // Normalize shape defensively
      const list = Array.isArray(res.data?.data) ? res.data.data : (Array.isArray(res.data) ? res.data : []);
      setConcerns(list);
    } catch (err) {
      console.error(t("Error fetching concerns"), err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchConcerns();
  }, []);

  const handleAddConcern = () => {
    resetForm();
    setShow(true);
  };

  const resetForm = () => {
    setConcern({ name: "", image: null });
    setImagePreview(null);
    setIsEditing(false);
    setEditingConcernId(null);
  };

  const handleConcernChange = (e) => {
    const { name, value } = e.target;
    setConcern((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files?.[0] || null;
    setConcern((prev) => ({ ...prev, image: file }));
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
    } else {
      setImagePreview(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("name", concern.name);
    if (concern.image instanceof File) formData.append("image", concern.image);

    try {
      const endpoint = isEditing ? `/concerns/${editingConcernId}` : "/concerns";
      const method = isEditing ? "put" : "post";

      const res = await axiosInstance[method](endpoint, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (res.status === 200 || res.status === 201) {
        await fetchConcerns();
        setShow(false);
        resetForm();
      }
    } catch (err) {
      console.error(t("Error saving concern"), err);
    }
  };

  const handleEditConcern = async (id) => {
    try {
      const res = await axiosInstance.get(`/concerns/${id}`);
      const item = res?.data || {};
      setConcern({ name: item.name || "", image: null });
      setImagePreview(item.image ? `${baseURL}${item.image}` : null);
      setEditingConcernId(id);
      setIsEditing(true);
      setShow(true);
    } catch (err) {
      console.error(t("Error fetching concern for editing"), err);
    }
  };

  const toggleIsActive = async (id, newStatus) => {
    try {
      await axiosInstance.put(`/concerns/is-active/${id}`, { is_active: newStatus });
      setConcerns((prev) => prev.map((c) => (c.id === id ? { ...c, is_active: newStatus } : c)));
    } catch (err) {
      console.error(t("Error updating status"), err);
    }
  };

  // -------- Table columns (render(value, row, idx)) --------
  const columns = [
    {
      key: "image",
      label: t("image"),
      align: "center",
      render: (value, row) => (
        <div className="flex items-center justify-center">
          <img
            src={value ? `${baseURL}${value}` : ""}
            alt="concern"
            className="w-20 h-20  object-cover rounded border cursor-pointer"
            onClick={() => navigate(`/dashboard/thoughts/${row.id}`)}
          />
        </div>
      ),
    },
    {
      key: "name",
      label: t("name"),
      align: "center",
      render: (value, row) => (
        <div
          type="button"
          className="w-full h-full flex items-center justify-center text-md font-semibold cursor-pointer hover:underline"
          onClick={() => navigate(`/dashboard/thoughts/${row.id}`)}
        >
          {value}
        </div>
      ),
    },
    {
      key: "is_active",
      label: t("status"),
      align: "center",
      render: (value, row) => (
        <button
          type="button"
          onClick={() => toggleIsActive(row.id, !value)}
          className={`relative inline-flex w-11 h-6 items-center rounded-full p-1 transition-colors ${value ? "bg-teal-600" : "bg-gray-300"
            }`}
          aria-label={value ? t("active") : t("inactive")}
          title={value ? t("active") : t("inactive")}
        >
          <span
            className={`w-4 h-4 bg-white rounded-full shadow-md transform transition-transform ${value ? "-translate-x-5" : "translate-x-0"
              }`}
          />
        </button>
      ),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => navigate(`/dashboard/thoughts/${row.id}`)}
            className="p-2 bg-blue-600 text-white rounded hover:bg-blue-500"
            title={t("view")}
          >
            <CgEye size={18} />
          </button>
          <button
            onClick={() => handleEditConcern(row.id)}
            className="p-2 bg-teal-600 text-white rounded hover:bg-teal-500"
            title={t("edit")}
          >
            <FaEdit size={18} />
          </button>
        </div>
      ),
    },
  ];

  if (isLoading) return <ProgressBarWidget />;

  return (
    <>
      {show && (
        <div className="fixed inset-0 z-50 bg-black/50 flex justify-center items-center p-4">
          <form
            onSubmit={handleSubmit}
            className="bg-white rounded-lg p-6 w-full max-w-2xl shadow-lg relative"
          >
            <button
              type="button"
              className="absolute top-3 right-3 text-red-600 hover:text-red-400"
              onClick={() => {
                setShow(false);
                resetForm();
              }}
              aria-label={t("close")}
            >
              <CgClose size={24} />
            </button>

            <h2 className="text-xl font-bold mb-4">
              {isEditing ? t("edit_concern") : t("create_new_concern")}
            </h2>

            <div className="mb-4">
              <label className="block mb-1 font-semibold">{t("name")}</label>
              <input
                type="text"
                name="name"
                value={concern.name}
                onChange={handleConcernChange}
                required
                className="w-full border rounded px-3 py-2"
              />
            </div>

            <div className="mb-4">
              <label className="block mb-1 font-semibold">{t("upload_image")}</label>
              <div className="flex items-center gap-4">
                <label className="cursor-pointer bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-500">
                  <input
                    type="file"
                    name="image"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                  <FaCloudUploadAlt className="inline mr-2" />
                  {t("choose_file")}
                </label>
                {imagePreview && (
                  <img
                    src={imagePreview}
                    alt="preview"
                    className="w-16 h-16 rounded border object-cover"
                  />
                )}
              </div>
            </div>

            <button
              type="submit"
              className="mt-4 bg-teal-600 text-white px-6 py-2 rounded hover:bg-teal-500"
            >
              {isEditing ? t("update_concern") : t("create_concern")}
            </button>
          </form>
        </div>
      )}

      <TableWidget
        title={t("all_concerns")}
        columns={columns}
        rows={concerns}
        vh={70}
        noDataMessage={t("no_data_available")}
        locationNames={`/${name}`}
        onAddClick={handleAddConcern}
      />
    </>
  );
};

export default Concerns;