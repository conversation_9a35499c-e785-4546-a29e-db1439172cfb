import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import TableWidget from "../../widgets/TableWidget";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { CgClose, CgTrash } from "react-icons/cg";
import { FaEdit } from "react-icons/fa";
import Logo from "../../assets/arabSBT_icon.png";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

const AddPages = () => {
    const { bookId, name } = useParams();
    const { t } = useTranslation();

    const [pages, setPages] = useState([]);
    const [openModal, setOpenModal] = useState(false);
    const [editModal, setEditModal] = useState(false);

    const [images, setImages] = useState([]);          // for Add (multi)
    const [previewImage, setPreviewImage] = useState(null); // for Edit (single string | null)

    const [formData, setFormData] = useState({
        pageId: null,
        bookId: bookId,
        pageImage: null,
    });

    const [loading, setLoading] = useState(true);

    const resolveUrl = (v) => {
        if (!v) return null;
        return /^https?:\/\//i.test(v) ? v : `${baseURL}${v}`;
    };

    const fetchPages = async () => {
        try {
            const response = await axiosInstance.get(`/books/get-pages/${bookId}`);
            if (response.data?.success) {
                setPages(Array.isArray(response.data.pages) ? response.data.pages : []);
            } else {
                setPages([]);
            }
        } catch (error) {
            if (error?.response?.status === 404) {
                setPages([]);
            } else {
                toast.error(t("failed_to_fetch_pages") || "Failed to fetch pages.");
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // artificial delay preserved from your code
        const timer = setTimeout(fetchPages, 3000);
        return () => clearTimeout(timer);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [bookId]);

    const handleSubmit = async () => {
        const form = new FormData();
        form.append("book_id", formData.bookId);

        if (editModal) {
            form.append("page_id", formData.pageId);
            if (images.length > 0) {
                form.append("page_image", images[0]);
            }
            try {
                await axiosInstance.put(`/books/pages/${formData.pageId}`, form, {
                    headers: { "Content-Type": "multipart/form-data" },
                });
                await fetchPages();
                toast.success(t("page_updated_successfully") || "Page updated successfully!");
                resetForm();
            } catch (error) {
                toast.error(
                    error?.response?.data?.error ||
                    t("failed_to_update_page") ||
                    "Failed to update page."
                );
            }
        } else {
            // add (multi-upload)
            images.forEach((image) => form.append("pages", image));
            try {
                await axiosInstance.post(`/books/add-pages/${bookId}`, form, {
                    headers: { "Content-Type": "multipart/form-data" },
                });
                await fetchPages();
                toast.success(t("page_added_successfully") || "Page added successfully!");
                resetForm();
                setOpenModal(false);
            } catch (error) {
                toast.error(
                    error?.response?.data?.error ||
                    t("failed_to_add_page") ||
                    "Failed to add pages."
                );
            }
        }
    };

    const handleDeletePage = async (pageId) => {
        try {
            await axiosInstance.delete(`/books/pages/${pageId}`);
            setPages((prev) => prev.filter((p) => p.id !== pageId));
            toast.success(t("page_deleted_successfully") || "Page deleted successfully.");
        } catch (error) {
            console.error("Delete error:", error);
            toast.error(t("failed_to_delete_page") || "Failed to delete page.");
        }
    };

    const handleImageChange = (e) => {
        const files = Array.from(e.target.files || []);
        setImages(files);

        // In edit mode, show a single preview. In add mode we show a grid below.
        if (files.length > 0) {
            setPreviewImage(URL.createObjectURL(files[0]));
        } else {
            setPreviewImage(null);
        }
    };

    const resetForm = () => {
        setOpenModal(false);
        setEditModal(false);
        setFormData({
            pageId: null,
            bookId: bookId,
            pageImage: null,
        });
        setImages([]);
        setPreviewImage(null);
    };

    // ---------- TableWidget columns/rows ----------
    const columns = [
        {
            key: "page_image",
            label: t("image"),
            align: "center",
            minWidth: 260,
            render: (value, row) => {
                const src = resolveUrl(value) || Logo;
                return (
                    <div className="flex items-center justify-center">
                        <img
                            src={src}
                            alt={`Page ${row?.id ?? ""}`}
                            className="w-52 h-66 object-fill border rounded-md"
                        />
                    </div>
                );
            },
        },
        {
            key: "created_at",
            label: t("date_added"),
            align: "center",
            minWidth: 160,
            render: (value) => (
                <span>{value ? new Date(value).toLocaleDateString() : "–"}</span>
            ),
        },
        {
            key: "actions",
            label: t("actions"),
            align: "center",
            minWidth: 220,
            render: (_value, row) => (
                <div className="flex items-center justify-center gap-2">
                    <button
                        onClick={() => {
                            setFormData({
                                pageId: row.id,
                                bookId,
                                pageImage: row.page_image,
                            });
                            setPreviewImage(resolveUrl(row.page_image));
                            setImages([]); // clear previous selection
                            setEditModal(true);
                            setOpenModal(true); // reuse the same modal container
                        }}
                        className="text-white bg-teal-700 hover:bg-teal-600 p-2 rounded-md"
                        title={t("edit")}
                    >
                        <FaEdit size={18} />
                    </button>
                    <button
                        onClick={() => handleDeletePage(row.id)}
                        className="text-white bg-red-700 hover:bg-red-600 p-2 rounded-md"
                        title={t("delete")}
                    >
                        <CgTrash size={18} />
                    </button>
                </div>
            ),
        },
    ];

    // rows can be the API array directly (keys match column.key)
    const rows = pages;

    if (loading) return <ProgressBarWidget />;

    return (
        <>
            <TableWidget
                locationNames={`${t("book")} ${name ?? ""}`}
                title={t("pages")}
                columns={columns}
                rows={rows}
                noDataMessage="no_data_available"     // let TableWidget translate the key
                isAddClick
                onAddClick={() => {
                    resetForm();
                    setOpenModal(true);
                    setEditModal(false);
                }}
                // Optional scrolling/paging knobs:
                pageSize={9}
                px={16}
                vh={80}
            // Enable both scroll directions when columns overflow:
            // scrollY="70vh"         // if your TableWidget supports it
            // minTableWidth={900}    // if your TableWidget supports it
            />

            {(openModal || editModal) && (
                <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-semibold">
                                {editModal ? t("edit_page") : t("add_page")}
                            </h2>
                            <button
                                onClick={resetForm}
                                className="text-white bg-red-700 hover:bg-red-500 p-1 rounded-full"
                            >
                                <CgClose size={20} />
                            </button>
                        </div>

                        {/* Single preview (edit) */}
                        {editModal && previewImage && (
                            <img
                                src={previewImage}
                                alt="Current Page"
                                className="w-52 h-66 object-fill border rounded-md mb-4"
                            />
                        )}

                        {/* Thumbnails grid (add mode) */}
                        {!editModal && images.length > 0 && (
                            <div className="grid grid-cols-2 md:grid-cols-3 max-h-[300px] overflow-y-auto gap-4 mb-3">
                                {images.map((image, index) => (
                                    <div
                                        key={index}
                                        className="relative border border-gray-300 rounded-md"
                                    >
                                        <img
                                            src={URL.createObjectURL(image)}
                                            alt={`Preview ${index}`}
                                            className="w-52 h-66 object-fill rounded-md"
                                        />
                                        <button
                                            onClick={() =>
                                                setImages((prev) => prev.filter((_, i) => i !== index))
                                            }
                                            className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full"
                                            title={t("remove")}
                                        >
                                            <CgClose size={12} />
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}

                        <input
                            type="file"
                            name="images"
                            multiple={!editModal}
                            accept="image/*"
                            onChange={handleImageChange}
                            className="w-full p-2 mb-3 border rounded-lg"
                        />

                        <button
                            onClick={handleSubmit}
                            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
                        >
                            {editModal ? t("update") : t("add_page")}
                        </button>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddPages;