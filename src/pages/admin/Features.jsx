/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import axiosInstance from '../../utils/instance_axios';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';
import TableWidget from '../../widgets/TableWidget';
import { FaEdit } from 'react-icons/fa';
import { CgTrash } from 'react-icons/cg';

const Features = () => {
  const { t, i18n } = useTranslation();
  const dir = i18n.dir();

  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);

  const [openFeatureModal, setOpenFeatureModal] = useState(false);
  const [editingFeatureId, setEditingFeatureId] = useState(null);
  const [formState, setFormState] = useState({
    name: '',
    type: '',
    discount_percentage: '',
  });

  /* ---------------- API ---------------- */
  const fetchFeatures = async () => {
    try {
      const res = await axiosInstance.get('/features');
      setFeatures(Array.isArray(res.data?.features) ? res.data.features : []);
    } catch (error) {
      console.error(error);
      toast.error(t('failed_to_fetch_features'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeatures();
  }, []);

  /* ---------------- Handlers ---------------- */
  const handleFeatureAdd = () => {
    setEditingFeatureId(null);
    setFormState({ name: '', type: '', discount_percentage: '' });
    setOpenFeatureModal(true);
  };

  const handleCloseFeatureModal = () => {
    setOpenFeatureModal(false);
    setEditingFeatureId(null);
    setFormState({ name: '', type: '', discount_percentage: '' });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const payload = {
      ...formState,
      discount_percentage:
        formState.discount_percentage === '' || formState.discount_percentage == null
          ? 0
          : parseFloat(formState.discount_percentage),
    };

    try {
      if (editingFeatureId) {
        await axiosInstance.put(`/features/${editingFeatureId}`, payload);
        toast.success(t('feature_updated_successfully'));
      } else {
        await axiosInstance.post('/features', payload);
        toast.success(t('feature_added_successfully'));
      }
      await fetchFeatures();
      handleCloseFeatureModal();
    } catch (error) {
      console.error(error);
      toast.error(editingFeatureId ? t('failed_to_update_feature') : t('failed_to_add_feature'));
    }
  };

  const handleEdit = (row) => {
    setEditingFeatureId(row.id);
    setFormState({
      name: row.name ?? '',
      type: row.type ?? '',
      discount_percentage:
        row.discount_percentage != null ? String(row.discount_percentage) : '',
    });
    setOpenFeatureModal(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm(t('are_you_sure_to_delete'))) return;
    try {
      await axiosInstance.delete(`/features/${id}`);
      toast.success(t('feature_deleted_successfully'));
      fetchFeatures();
    } catch (error) {
      console.error(error);
      toast.error(t('failed_to_delete_feature'));
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  /* ---------------- Table (columns + rows) ---------------- */
  const columns = [
    {
      key: 'index',
      label: t('id'),
      align: 'center',
      minWidth: 80,
      render: (_value, _row, index) => index + 1,
    },
    {
      key: 'name',
      label: t('name'),
      align: 'center',
      minWidth: 200,
      render: (value) => value || '—',
    },
    {
      key: 'type',
      label: t('type'),
      align: 'center',
      minWidth: 160,
      render: (value) => value || '—',
    },
    {
      key: 'discount_percentage',
      label: t('discount'),
      align: 'center',
      minWidth: 160,
      render: (value) =>
        value != null && value !== '' ? `${Number(value)}%` : '—',
    },
    {
      key: 'actions',
      label: t('actions'),
      align: 'center',
      minWidth: 180,
      render: (_value, row) => (
        <div className="flex items-center gap-2 justify-center">
          <button
            className="bg-teal-600 hover:bg-teal-500 text-white p-2 rounded"
            onClick={() => handleEdit(row)}
            title={t('edit')}
          >
            <FaEdit size={18} />
          </button>
          <button
            className="bg-red-600 hover:bg-red-500 text-white p-2 rounded"
            onClick={() => handleDelete(row.id)}
            title={t('delete')}
          >
            <CgTrash size={18} />
          </button>
        </div>
      ),
    },
  ];

  if (loading) return <ProgressBarWidget />;

  return (
    <div className="p-4 font-sans" dir={dir}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">{t('features')}</h2>
      </div>

      <TableWidget
        title={t('features')}
        columns={columns}
        rows={features}
        noDataMessage={t('no_data_available')}
        onAddClick={handleFeatureAdd}
        vh={80}
        scrollX
        minTableWidth={900}
        clampLines={1}
        ellipsisTooltip
      />

      {openFeatureModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-[400px] font-sans">
            <h2 className="text-xl font-semibold text-gray-800 text-center">
              {editingFeatureId ? t('edit_feature') : t('add_feature')}
            </h2>
            <p className="text-gray-600 mt-2 text-center">{t('enter_feature_details')}</p>

            <form onSubmit={handleSubmit} className="mt-4 space-y-4">
              {/* Name */}
              <div className="relative w-full">
                <input
                  type="text"
                  name="name"
                  id="feature-name"
                  value={formState.name}
                  onChange={handleInputChange}
                  required
                  placeholder={t('name')}
                  className="peer w-full border border-gray-300 rounded-md px-3 pt-5 pb-2 text-sm bg-white placeholder-transparent focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                />
                <label
                  htmlFor="feature-name"
                  className={`absolute ${dir === 'rtl' ? 'right-3' : 'left-3'} px-2 bg-white text-sm text-gray-400 font-bold transition-all duration-200 pointer-events-none
                  ${formState.name ? '-top-2 text-teal-500 text-xs border-2 border-teal-600 rounded-md' : 'top-3.5 text-sm'}
                  peer-focus:-top-2 peer-focus:text-teal-500 peer-focus:text-xs peer-focus:border-2 peer-focus:border-teal-500 peer-focus:rounded-md`}
                >
                  {t('name')}
                </label>
              </div>

              {/* Type */}
              <div className="relative w-full">
                <input
                  type="text"
                  name="type"
                  id="feature-type"
                  value={formState.type}
                  onChange={handleInputChange}
                  placeholder={t('type')}
                  className="peer w-full border border-gray-300 rounded-md px-3 pt-5 pb-2 text-sm bg-white placeholder-transparent focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                />
                <label
                  htmlFor="feature-type"
                  className={`absolute ${dir === 'rtl' ? 'right-3' : 'left-3'} px-2 bg-white text-sm text-gray-400 font-bold transition-all duration-200 pointer-events-none
                  ${formState.type ? '-top-2 text-teal-500 text-xs border-2 border-teal-600 rounded-md' : 'top-3.5 text-sm'}
                  peer-focus:-top-2 peer-focus:text-teal-500 peer-focus:text-xs peer-focus:border-2 peer-focus:border-teal-500 peer-focus:rounded-md`}
                >
                  {t('type')}
                </label>
              </div>

              {/* Discount */}
              <div className="relative w-full">
                <input
                  type="number"
                  name="discount_percentage"
                  id="feature-discount"
                  value={formState.discount_percentage}
                  onChange={handleInputChange}
                  placeholder={t('discount_percentage')}
                  className="peer w-full border border-gray-300 rounded-md px-3 pt-5 pb-2 text-sm bg-white placeholder-transparent focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                />
                <label
                  htmlFor="feature-discount"
                  className={`absolute ${dir === 'rtl' ? 'right-3' : 'left-3'} px-2 bg-white text-sm text-gray-400 font-bold transition-all duration-200 pointer-events-none
                  ${formState.discount_percentage ? '-top-2 text-teal-500 text-xs border-2 border-teal-600 rounded-md' : 'top-3.5 text-sm'}
                  peer-focus:-top-2 peer-focus:text-teal-500 peer-focus:text-xs peer-focus:border-2 peer-focus:border-teal-500 peer-focus:rounded-md`}
                >
                  {t('discount_percentage')}
                </label>
              </div>

              {/* Buttons */}
              <div className="flex justify-between mt-6">
                <button
                  type="submit"
                  className="bg-teal-600 hover:bg-teal-500 text-white font-bold py-2 px-4 rounded"
                >
                  {t('save')}
                </button>
                <button
                  type="button"
                  onClick={handleCloseFeatureModal}
                  className="bg-red-600 hover:bg-red-500 text-white font-bold py-2 px-4 rounded"
                >
                  {t('cancel')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Features;