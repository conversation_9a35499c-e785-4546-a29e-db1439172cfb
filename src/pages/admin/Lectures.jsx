/* eslint-disable react-hooks/exhaustive-deps */
import { useTranslation } from "react-i18next";
import TableWidget from "../../widgets/TableWidget";
import { useState, useEffect } from "react";
import { CgClose, CgTrash } from "react-icons/cg";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { toast } from "react-toastify";
import { FaEdit } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

const Lectures = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [openModal, setOpenModal] = useState(false);
  const [lectures, setLectures] = useState([]);
  const [formState, setFormState] = useState({
    title: "",
    type: "category",
    description: "",
    image: null,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [currentLectureId, setCurrentLectureId] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchLectures = async () => {
    try {
      const response = await axiosInstance.get("/lectures");
      const data = response.data?.data;
      setLectures(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching lectures:", error);
      setLectures([]);
      toast.error(t("something_went_wrong", "Something went wrong"));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setTimeout(() => {
      fetchLectures();
    }, 3000); // Simulate loading delay
  }, []);

  const showModal = () => setOpenModal(true);

  const handleCreate = async (e) => {
    e.preventDefault();
    const fd = new FormData();
    fd.append("text", formState.title?.trim() || "");
    fd.append("type", formState.type || "category");
    fd.append("description", formState.description ?? "");
    if (formState.image instanceof File) fd.append("image", formState.image);

    try {
      let response;
      if (isEditing && currentLectureId) {
        response = await axiosInstance.put(`/lectures/update/${currentLectureId}`, fd, {
          headers: { "Content-Type": "multipart/form-data" },
        });
        toast.success(t("lecture_updated_success", "Lecture updated successfully!"));
      } else {
        response = await axiosInstance.post("/lectures/create", fd, {
          headers: { "Content-Type": "multipart/form-data" },
        });
        toast.success(t("lecture_added_success", "Lecture added successfully!"));
      }
      if (response.status === 200 || response.status === 201) {
        setOpenModal(false);
        setFormState({ title: "", type: "category", description: "", image: null });
        setIsEditing(false);
        setCurrentLectureId(null);
        fetchLectures();
      }
    } catch (error) {
      console.error("Failed to save lecture:", error);
      toast.error(t("failed_to_save_lecture", "Failed to save lecture. Please try again."));
    }
  };

  const handleDelete = async (id) => {
    try {
      const response = await axiosInstance.delete(`/lectures/delete/${id}`);
      if (response.status === 200) {
        toast.success(t("lecture_deleted_success", "Lecture deleted successfully!"));
        fetchLectures();
      }
    } catch (error) {
      console.error("Failed to delete lecture:", error);
      toast.error(t("failed_to_delete_lecture", "Failed to delete lecture. Please try again."));
    }
  };

  const handleEdit = async (id) => {
    try {
      const response = await axiosInstance.get(`/lectures/${id}`);
      if (response.status === 200) {
        const data = response.data.data;
        setFormState({
          title: data?.text ?? "",
          type: data?.type ?? "category",
          description: data?.description ?? "",
          image: data?.image ?? null,
        });
        setCurrentLectureId(id);
        setIsEditing(true);
        setOpenModal(true);
      }
    } catch (error) {
      console.error("Failed to load lecture:", error);
      toast.error(t("failed_to_edit_lecture", "Failed to edit lecture. Please try again."));
    }
  };

  const handleToggle = async (lectureId, currentStatus) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    try {
      const response = await axiosInstance.put(`/lectures/is-active/${lectureId}`, {
        is_active: newStatus,
      });
      if (response.status === 200 || response.data?.success) {
        setLectures((prev) =>
          prev.map((lecture) =>
            lecture.id === lectureId ? { ...lecture, is_active: newStatus } : lecture
          )
        );
        toast.success(t("lecture_status_updated", "Lecture status updated successfully!"));
      } else {
        throw new Error("Unexpected response format");
      }
    } catch (error) {
      console.error("Error updating lecture status:", error);
      toast.error(t("failed_to_update_status", "Failed to update lecture status."));
    }
  };

  const closeModal = () => {
    setOpenModal(false);
    setFormState({ title: "", type: "category", description: "", image: null });
    setIsEditing(false);
    setCurrentLectureId(null);
  };

  if (isLoading) return <ProgressBarWidget />;

  // ----- COLUMNS: render(value, row, index) -----
  const columns = [
    {
      key: "id",
      label: t("id"),
      align: "center",
      minWidth: 80,
      render: (value) => <div className="text-center">{value}</div>,
    },
    {
      key: "image",
      label: t("image"),
      align: "center",
      minWidth: 120,
      render: (value, row) => (
        <div className="text-center">
          {value ? (
            <img
              src={`${baseURL}${value}`}
              alt={row?.text || "lecture"}
              className="w-20 h-20 object-cover rounded"
              loading="lazy"
            />
          ) : (
            <span className="text-gray-400 italic">{t("no_image", "No image")}</span>
          )}
        </div>
      ),
    },
    {
      key: "text",
      label: t("name"),
      align: "center",
      minWidth: 220,
      render: (value, row) => (
        <button
          type="button"
          className="text-center max-w-[280px] whitespace-nowrap overflow-hidden text-ellipsis hover:underline hover:text-[#51B4E2]"
          title={value}
          onClick={() => navigate(`/dashboard/lectures/${row.id}`)}
        >
          {value}
        </button>
      ),
    },
    {
      key: "description",
      label: t("description"),
      align: "center",
      minWidth: 300,
      render: (value) => (
        <div
          className="mx-auto max-w-[420px] text-center whitespace-nowrap overflow-hidden text-ellipsis"
          title={value || ""}
        >
          {value || "—"}
        </div>
      ),
    },
    {
      key: "type",
      label: t("type"),
      align: "center",
      minWidth: 140,
      render: (value) => (
        <div className="text-center">
          {value === "category" ? t("category") : t("sub_category")}
        </div>
      ),
    },
    {
      key: "is_active",
      label: t("status"),
      align: "center",
      minWidth: 140,
      render: (value, row) => (
        <div className="flex justify-center">
          <label className="relative inline-flex items-center cursor-pointer" title={value ? t("active") : t("inactive")}>
            <input
              type="checkbox"
              checked={value === 1}
              onChange={() => handleToggle(row.id, value)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-300 rounded-full peer-checked:bg-teal-500 transition-colors duration-300" />
            <div className="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-300 peer-checked:translate-x-5" />
          </label>
        </div>
      ),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 160,
      render: (_value, row) => (
        <div className="flex justify-center gap-2">
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded"
            onClick={() => handleEdit(row.id)}
            title={t("edit")}
          >
            <FaEdit size={18} />
          </button>
          <button
            className="bg-red-500 hover:bg-red-600 text-white p-2 rounded"
            onClick={() => handleDelete(row.id)}
            title={t("delete")}
          >
            <CgTrash size={18} />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <TableWidget
        title={t("lectures")}
        columns={columns}
        rows={lectures}
        noDataMessage={t("no_data_available")}
        // keep things tidy when content is wide
        scrollX
        minTableWidth={1000}
        pageSize={10}
        vh={80}
        isAddClick
        onAddClick={showModal}
      />

      {openModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
          <div className="bg-white rounded-lg p-6 w-[600px] shadow-lg">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl text-right font-semibold">
                {isEditing ? t("edit_lecture") : t("add_lecture")}
              </h2>
              <button
                className="bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                onClick={closeModal}
                aria-label="Close"
              >
                <CgClose size={20} />
              </button>
            </div>

            <form onSubmit={handleCreate} className="space-y-4">
              <div>
                <label className="block text-gray-700 font-semibold mb-1">{t("name")}</label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-md p-2"
                  name="title"
                  value={formState.title}
                  onChange={(e) => setFormState({ ...formState, title: e.target.value })}
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 font-semibold mb-1">{t("type")}</label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                  name="type"
                  value={formState.type === "category" ? t("category") : t("sub_category")}
                  readOnly
                />
              </div>

              <div>
                <label className="block text-gray-700 font-semibold mb-1">{t("description")}</label>
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2 resize-none"
                  name="description"
                  rows="3"
                  value={formState.description}
                  onChange={(e) => setFormState({ ...formState, description: e.target.value })}
                />
              </div>

              <div>
                <label className="block text-gray-700 font-semibold mb-1">{t("image")}</label>

                {isEditing && currentLectureId && formState.image && (
                  <div className="mb-2">
                    <img
                      src={baseURL + (lectures.find((l) => l.id === currentLectureId)?.image || "")}
                      alt="Current"
                      className="w-32 h-32 rounded-md object-cover border"
                    />
                  </div>
                )}

                <input
                  type="file"
                  accept="image/*"
                  className="w-full border border-gray-300 rounded-md p-2"
                  onChange={(e) => setFormState({ ...formState, image: e.target.files?.[0] || null })}
                />
              </div>

              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  className="bg-teal-700 hover:bg-teal-600 text-white px-4 py-2 rounded-md"
                >
                  {isEditing ? t("update") : t("save")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default Lectures;