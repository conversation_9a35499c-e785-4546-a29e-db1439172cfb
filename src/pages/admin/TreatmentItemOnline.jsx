import { useTranslation } from "react-i18next";
import { useState, useEffect, useRef } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { useParams } from "react-router-dom";
import TableWidget from "../../widgets/TableWidget";
import { FaEdit } from "react-icons/fa";
import { MdDelete } from "react-icons/md";

const TreatmentItemOnline = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const isMobile = window.innerWidth < 768;

  const [items, setItems] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [editItem, setEditItem] = useState(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [countdown, setCountdown] = useState(null);
  const [uploadTotalSeconds, setUploadTotalSeconds] = useState(0);
  const [uploadPercent, setUploadPercent] = useState(0);
  const [conversionPercent, setConversionPercent] = useState(0);
  const [isConverting, setIsConverting] = useState(false);
  const [uploadDone, setUploadDone] = useState(false);
  const [uploadLoadedBytes, setUploadLoadedBytes] = useState(0);
  const [uploadTotalBytes, setUploadTotalBytes] = useState(0);

  const [formData, setFormData] = useState({
    text: "",
    image: null,
    file: null,
    type: "item",
  });

  const [previewVideo, setPreviewVideo] = useState(null);

  const fileInputRef = useRef(null);
  const imageInputRef = useRef(null);

  const fetchItems = async (itemId) => {
    if (!itemId) return;
    try {
      const res = await axiosInstance.get(`/videos/treatment-item/item/${itemId}`);
      setItems(res.data?.data || []);
    } catch (err) {
      console.error("Error fetching items:", err);
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const calculateEstimatedTime = (fileSizeInMB) => Math.ceil(fileSizeInMB * 1.5);

  useEffect(() => {
    fetchItems(id);
  }, [id]);

  useEffect(() => {
    if (isSubmitting && uploadTotalSeconds > 0) {
      setCountdown(uploadTotalSeconds);
      const interval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isSubmitting, uploadTotalSeconds]);

  useEffect(() => {
    if (typeof countdown === "number" && uploadTotalSeconds > 0) {
      const elapsed = uploadTotalSeconds - countdown;
      const percent = Math.min(Math.round((elapsed / uploadTotalSeconds) * 100), 100);
      setUploadPercent(percent);
    }
  }, [countdown, uploadTotalSeconds]);

  const openModal = (item = null) => {
    setEditItem(item);
    setFormData({
      text: item?.text || "",
      image: null,
      file: null,
      type: "item",
    });
    setPreviewVideo(item);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditItem(null);
    setFormData({
      text: "",
      image: null,
      file: null,
      type: "item",
    });
    setPreviewVideo(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
    if (imageInputRef.current) imageInputRef.current.value = "";
  };

  const handleChange = (e) => {
    const { name, files, value, type } = e.target;
    const file = type === "file" ? files[0] : null;

    if (name === "image" && file) {
      const fileSizeMB = file.size / (1024 * 1024);
      const estimatedTime = calculateEstimatedTime(fileSizeMB);
      setUploadTotalSeconds(estimatedTime);
      setUploadPercent(0);
    }

    setFormData((prev) => ({
      ...prev,
      [name]: file || value,
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setUploadPercent(0);
    setUploadDone(false);
    setConversionPercent(0);
    setIsConverting(false);

    try {
      const data = new FormData();
      data.append("text", formData.text);
      data.append("type", formData.type);
      data.append("video_id", parseInt(id, 10));

      if (formData.image) data.append("image", formData.image);
      if (formData.file) data.append("file", formData.file);

      const config = {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: (progressEvent) => {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadPercent(percent);
          setUploadLoadedBytes(progressEvent.loaded);
          setUploadTotalBytes(progressEvent.total);
        },
      };

      let response;
      if (editItem) {
        response = await axiosInstance.put(
          `/videos/treatment-item/item/${editItem.id}`,
          data,
          config
        );
      } else {
        response = await axiosInstance.post(`/videos/treatment-item/item`, data, config);
      }

      if ([200, 201].includes(response.status)) {
        setUploadPercent(100);
        setUploadDone(true);
        setIsConverting(true);

        let current = 0;
        const interval = setInterval(() => {
          current += 10;
          if (current >= 100) {
            clearInterval(interval);
            setConversionPercent(100);
            setTimeout(() => {
              setIsConverting(false);
              setConversionPercent(0);
              fetchItems(id);
              closeModal();
            }, 500);
          } else {
            setConversionPercent(current);
          }
        }, 300);
      }
    } catch (err) {
      console.error("Submit failed:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (itemId) => {
    if (!window.confirm(t("are_you_sure_delete"))) return;
    try {
      await axiosInstance.delete(`/videos/treatment-item/item/${itemId}`);
      fetchItems(id);
    } catch (err) {
      console.error("Delete failed:", err);
    }
  };

  // ------- COLUMNS: render(value, row, index) -------
  const columns = [
    {
      key: "id",
      label: t("id"),
      align: "center",
      minWidth: 80,
      render: (_value, _row, index) => <span className="font-semibold">{index + 1}</span>,
    },
    {
      key: "text",
      label: t("text"),
      align: "center",
      minWidth: 220,
      render: (value) => <div className="text-center line-clamp-2" title={value}>{value}</div>,
    },
    {
      key: "image",
      label: t("video"),
      align: "center",
      minWidth: 300,
      render: (value) => (
        <div className="text-center">
          {value ? (
            <video
              controls
              src={baseURL + value}
              className="w-[250px] h-[150px] mx-auto object-cover rounded"
            />
          ) : (
            <span className="text-gray-400 text-sm">{t("no_video")}</span>
          )}
        </div>
      ),
    },
    {
      key: "file",
      label: t("file"),
      align: "center",
      minWidth: 320,
      render: (value) => (
        <div className="text-center w-full max-w-[300px] mx-auto overflow-y-auto rounded shadow border">
          {value && value.endsWith(".pdf") ? (
            <iframe
              src={`${baseURL}${value}`}
              title={value}
              className="w-full h-[240px]"
              frameBorder="0"
            />
          ) : (
            <p className="text-gray-400 text-sm">{t("no_pdf_available")}</p>
          )}
        </div>
      ),
    },
    {
      key: "type",
      label: t("type"),
      align: "center",
      minWidth: 120,
      render: (value) => <div className="text-center">{t(value || "item")}</div>,
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 180,
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => openModal(row)}
            className="text-white bg-teal-600 hover:bg-teal-500 p-1 rounded-md"
            title={t("edit")}
          >
            <FaEdit size={18} />
          </button>
          <button
            onClick={() => handleDelete(row.id)}
            className="text-white bg-red-600 hover:bg-red-700 p-1 rounded-md"
            title={t("delete")}
          >
            <MdDelete size={18} />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <TableWidget
        title={t("treatment_item")}
        columns={columns}
        rows={items}
        noDataMessage={t("no_data_available")}
        px={18}
        vh={90}
        isMobile={isMobile}
        pageSize={isMobile ? 3 : 5}
        // keep the table responsive when columns overflow:
        scrollX
        minTableWidth={isMobile ? 900 : 1100}
        clampLines={2}
        ellipsisTooltip
        isAddClick
        onAddClick={() => openModal()}
      />

      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4">
          <div className="bg-white p-6 rounded-md shadow-xl w-full max-w-lg">
            <h2 className="text-2xl font-semibold mb-4">
              {editItem ? t("edit_item") : t("create_new_item")}
            </h2>

            <label className="block mb-2 font-medium">{t("title")}</label>
            <input
              type="text"
              name="text"
              value={formData.text}
              onChange={handleChange}
              className="w-full mb-4 p-2 border rounded"
              placeholder={t("title")}
            />

            <label className="block mb-2 font-medium">{t("upload_video")}</label>
            <input
              type="file"
              name="image"
              accept="video/*"
              ref={imageInputRef}
              onChange={handleChange}
              className="w-full mb-4 p-2 border rounded"
            />

            {(formData.image || previewVideo?.image) && (
              <video
                controls
                className="w-full h-[200px] rounded border border-gray-300"
                src={
                  formData.image
                    ? URL.createObjectURL(formData.image)
                    : baseURL + (previewVideo?.image || "")
                }
              />
            )}

            <label className="block mb-2 font-medium">{t("file")}</label>
            <input
              type="file"
              name="file"
              accept="*/*"
              ref={fileInputRef}
              onChange={handleChange}
              className="w-full mb-4 p-2 border rounded"
            />

            <div className="flex justify-end gap-3 mt-4">
              <button
                onClick={closeModal}
                disabled={isSubmitting}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-400"
              >
                {t("cancel")}
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700 disabled:opacity-60"
              >
                {isSubmitting ? t("uploading") : editItem ? t("update") : t("create")}
              </button>
            </div>

            {isSubmitting && (
              <div className="mt-4 text-center text-sm text-teal-600">
                {!uploadDone && (
                  <>
                    <p>{t("please_wait_uploading_video")}</p>
                    <progress value={uploadPercent} max="100" className="w-full h-3 my-2 rounded" />
                    <p className="text-xs">{uploadPercent}%</p>
                  </>
                )}
                <p className="text-xs">
                  {formatBytes(uploadLoadedBytes)} / {formatBytes(uploadTotalBytes)}
                </p>
                {uploadDone && isConverting && (
                  <>
                    <p>{t("please_wait_processing_video")}</p>
                    <progress value={conversionPercent} max="100" className="w-full h-3 my-2 rounded" />
                    <p className="text-xs">{conversionPercent}%</p>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default TreatmentItemOnline;