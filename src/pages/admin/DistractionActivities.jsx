import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { FaEdit } from "react-icons/fa";
import { CgClose, CgTrash } from "react-icons/cg";
import { useNavigate } from "react-router-dom";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

/** Normalize a possibly-relative URL to an absolute URL */
const toUrl = (u) => (u ? (/^https?:\/\//i.test(u) ? u : baseURL + u) : "");

const DistractionActivities = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [distractions, setDistractions] = useState([]);
  const [loading, setLoading] = useState(true);

  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    id: null,
    name: "",
    description: "",
    image: null, // File or null (when editing we show existing image separately)
  });
  const [editingImageUrl, setEditingImageUrl] = useState(null); // server image preview while editing

  const fetchDistractions = async () => {
    try {
      const { data } = await axiosInstance.get("/distraction");
      const items = Array.isArray(data?.data) ? data.data : [];
      setDistractions(items);
    } catch (error) {
      console.error("Error fetching distractions:", error?.message || error);
      setDistractions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(fetchDistractions, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e) => {
    const { files } = e.target;
    const file = files?.[0] || null;
    setFormData((prev) => ({ ...prev, image: file }));
  };

  const resetFormData = () => {
    setFormData({ id: null, name: "", description: "", image: null });
    setEditingImageUrl(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const submitData = new FormData();
    submitData.append("title", formData.name.trim());
    submitData.append("description", formData.description || "");
    if (formData.image instanceof File) {
      submitData.append("image", formData.image);
    }

    try {
      if (formData.id) {
        const res = await axiosInstance.put(
          `/distraction/update/${formData.id}`,
          submitData,
          { headers: { "Content-Type": "multipart/form-data" } }
        );
        if (res.status === 200) {
          setShowModal(false);
          fetchDistractions();
          resetFormData();
        }
      } else {
        const res = await axiosInstance.post("/distraction/create", submitData, {
          headers: { "Content-Type": "multipart/form-data" },
        });
        if (res.status === 201) {
          setShowModal(false);
          fetchDistractions();
          resetFormData();
        }
      }
    } catch (error) {
      console.error("Error submitting distraction:", error?.message || error);
    }
  };

  const handleEdit = (item) => {
    setFormData({
      id: item.id,
      name: item.title || "",
      description: item.description || "",
      image: null, // keep null so we can detect when user selects a new file
    });
    setEditingImageUrl(toUrl(item.image)); // show existing server image
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    try {
      await axiosInstance.delete(`/distraction/delete/${id}`);
      fetchDistractions();
    } catch (error) {
      console.error("Error deleting distraction:", error?.message || error);
    }
  };

  // ---- TableWidget columns: render(value, row, index) ----
  const columns = [
    {
      key: "id",
      label: "ID",
      align: "center",
      minWidth: 80,
      render: (_value, _row, index) => index + 1,
    },
    {
      key: "image",
      label: t("image"),
      align: "center",
      minWidth: 220,
      render: (value, row) => {
        const src = toUrl(value);
        return (
          <div
            className="cursor-pointer w-full h-full flex items-center justify-center"
            onClick={() => navigate(`/dashboard/distractions/${row.id}`)}
            title={t("view")}
          >
            {src ? (
              <img
                src={src}
                alt={row.title || "image"}
                className="w-[200px] h-[120px] object-cover rounded border"
                loading="lazy"
                onError={(e) => {
                  e.currentTarget.replaceWith(
                    Object.assign(document.createElement("div"), {
                      className:
                        "w-[200px] h-[120px] flex items-center justify-center rounded border bg-gray-100 text-gray-400 text-xs",
                      innerText: t("image_unavailable") || "Image unavailable",
                    })
                  );
                }}
              />
            ) : (
              <div className="w-[200px] h-[120px] flex items-center justify-center rounded border bg-gray-100 text-gray-400 text-xs">
                {t("no_image")}
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: "title",
      label: t("name"),
      align: "center",
      minWidth: 220,
      render: (value, row) => (
        <div
          className="cursor-pointer w-full h-full flex items-center justify-center"
          onClick={() => navigate(`/dashboard/distractions/${row.id}`)}
          title={value || ""}
        >
          <p className="text-center font-bold whitespace-nowrap overflow-hidden text-ellipsis max-w-[260px]">
            {value || "—"}
          </p>
        </div>
      ),
    },
    {
      key: "description",
      label: t("description"),
      align: "center",
      minWidth: 320,
      render: (value, row) => (
        <div
          className="cursor-pointer w-full h-full flex items-center justify-center"
          onClick={() => navigate(`/dashboard/distractions/${row.id}`)}
          title={value || ""}
        >
          <p className="text-center whitespace-nowrap overflow-hidden text-ellipsis max-w-[420px]">
            {value || "—"}
          </p>
        </div>
      ),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 160,
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            className="bg-teal-600 text-white p-2 rounded-md hover:bg-teal-500"
            onClick={() => handleEdit(row)}
            title={t("edit")}
          >
            <FaEdit size={18} />
          </button>
          <button
            className="bg-red-500 text-white p-2 rounded-md hover:bg-red-600"
            onClick={() => handleDelete(row.id)}
            title={t("delete")}
          >
            <CgTrash size={18} />
          </button>
        </div>
      ),
    },
  ];

  if (loading) return <ProgressBarWidget />;

  return (
    <>
      <TableWidget
        title={t("distractions_activities")}
        columns={columns}
        rows={distractions}
        noDataMessage={t("no_data_available")}
        isAddClick
        onAddClick={() => {
          resetFormData();
          setShowModal(true);
        }}
        // Nice defaults for responsiveness and truncation
        vh={85}
        scrollX
        minTableWidth={1000}
        clampLines={1}
        ellipsisTooltip
      />

      {showModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
          <div className="bg-white p-6 rounded-lg w-[520px] max-w-[95vw]">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold">
                {formData.id ? t("edit-distraction-activity") : t("add-distraction-activity")}
              </h2>
              <button
                className="text-red-600"
                onClick={() => {
                  setShowModal(false);
                  resetFormData();
                }}
                aria-label="Close"
              >
                <CgClose size={20} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium">
                  {t("name")}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium">
                  {t("description")}
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded resize-none"
                  rows={3}
                />
              </div>

              {/* Image upload */}
              <div>
                <label htmlFor="image" className="block text-sm font-medium">
                  {t("image")}
                </label>
                {formData.id && editingImageUrl && !formData.image && (
                  <div className="mb-2">
                    <img
                      src={editingImageUrl}
                      alt="current"
                      className="w-32 h-20 object-cover rounded border"
                    />
                  </div>
                )}
                <input
                  type="file"
                  id="image"
                  name="image"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full px-3 py-2 border rounded"
                />
                {formData.image && (
                  <div className="mt-2">
                    <img
                      src={URL.createObjectURL(formData.image)}
                      alt="preview"
                      className="w-32 h-20 object-cover rounded border"
                    />
                  </div>
                )}
              </div>

              <div className="text-right">
                <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded">
                  {t("submit")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default DistractionActivities;