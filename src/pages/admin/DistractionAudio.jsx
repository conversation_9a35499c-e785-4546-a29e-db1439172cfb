/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { CgClose, CgTrash } from "react-icons/cg";
import { useParams } from "react-router-dom";
import { FaEdit } from "react-icons/fa";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

const toUrl = (u) => (u ? (/^https?:\/\//i.test(u) ? u : baseURL + u) : "");

const DistractionAudio = () => {
  const { id } = useParams();
  const { t } = useTranslation();

  const [audioList, setAudioList] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const [openModal, setOpenModal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [title, setTitle] = useState("");
  const [editingAudio, setEditingAudio] = useState(null);

  const fetchAudio = async () => {
    try {
      const res = await axiosInstance.get(`/distraction/sub/${id}`);
      const items = res?.data?.data?.sub_distractions;
      setAudioList(Array.isArray(items) ? items : []);
    } catch (error) {
      console.error("Error fetching audio:", error?.message || error);
      setAudioList([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(fetchAudio, 1000);
    return () => clearTimeout(timer);
  }, [id]);

  const handleOpenModal = (audio = null) => {
    setEditingAudio(audio);
    setTitle(audio?.title || "");
    setUploadedFile(null);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setUploadedFile(null);
    setTitle("");
    setEditingAudio(null);
  };

  const handleSave = async (e) => {
    e.preventDefault();
    const trimmed = title.trim();
    if (!trimmed) return;

    const formData = new FormData();
    formData.append("title", trimmed);
    if (uploadedFile) formData.append("audio_url", uploadedFile);

    try {
      if (editingAudio) {
        await axiosInstance.put(`/distraction/update-audio/${editingAudio.id}`, formData, {
          headers: { "Content-Type": "multipart/form-data" },
        });
      } else {
        formData.append("distraction_id", id);
        await axiosInstance.post(`/distraction/create-audio/${id}`, formData, {
          headers: { "Content-Type": "multipart/form-data" },
        });
      }
      fetchAudio();
      handleCloseModal();
    } catch (error) {
      console.error("Error saving audio:", error?.message || error);
    }
  };

  const handleDelete = async (audioId) => {
    try {
      await axiosInstance.delete(`/distraction/delete-audio/${audioId}`);
      fetchAudio();
    } catch (error) {
      console.error("Error deleting audio:", error?.message || error);
    }
  };

  // ---------- Columns: render(value, row, index) ----------
  const columns = [
    {
      key: "id",
      label: t("id"),
      align: "center",
      minWidth: 80,
      render: (_value, _row, index) => (
        <p className="flex items-center justify-center w-full h-full">{index + 1}</p>
      ),
    },
    {
      key: "title",
      label: t("title"),
      align: "center",
      minWidth: 240,
      render: (value) => (
        <div className="flex items-center justify-center w-full h-full">
          <p className="max-w-[320px] text-center whitespace-nowrap overflow-hidden text-ellipsis">
            {value || "—"}
          </p>
        </div>
      ),
    },
    {
      key: "audio_url", // <-- match your data field name
      label: t("audio"),
      align: "center",
      minWidth: 320,
      render: (value, _row) => {
        return (
          <div className="flex items-center justify-center gap-2 w-full">
            {value ? (
              <audio controls preload="none" className="max-w-[420px]">
                <source src={baseURL + value} />
                Your browser does not support the audio element.
              </audio>
            ) : (
              <span className="text-gray-400 text-sm">{t("no_data_available")}</span>
            )}
          </div>
        );
      },
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 160,
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            className="text-white bg-teal-700 p-2 rounded-lg hover:bg-teal-600"
            onClick={() => handleOpenModal(row)}
            title={t("edit")}
          >
            <FaEdit size={20} />
          </button>
          <button
            className="text-white bg-red-700 p-2 rounded-lg hover:bg-red-600"
            onClick={() => {
              if (window.confirm(t("are_you_sure_delete_item"))) handleDelete(row.id);
            }}
            title={t("delete")}
          >
            <CgTrash size={20} />
          </button>
        </div>
      ),
    },
  ];

  if (isLoading) return <ProgressBarWidget />;

  return (
    <>
      <TableWidget
        title={t("distraction_audio")}
        columns={columns}
        rows={audioList}
        vh={85}
        pageSize={14}
        noDataMessage={t("no_data_available")}
        isAddClick
        onAddClick={() => handleOpenModal()}
        scrollX
        minTableWidth={900}
        clampLines={1}
        ellipsisTooltip
      />

      {openModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 px-4">
          <form onSubmit={handleSave} className="bg-white p-5 rounded-lg shadow-lg w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <p className="font-semibold">
                {editingAudio ? t("edit_audio") : t("create_audio")}
              </p>
              <button
                className="bg-red-700 text-white p-1 rounded-full hover:bg-red-600"
                onClick={handleCloseModal}
                type="button"
                aria-label="Close"
              >
                <CgClose size={20} />
              </button>
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                {t("title")}
              </label>
              <input
                type="text"
                value={title}
                className="w-full border border-gray-300 p-2 rounded-lg"
                placeholder={t("enter_title")}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>

            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                {t("audio")}
              </label>
              <input
                type="file"
                accept="audio/*"
                className="w-full border border-gray-300 p-2 rounded-lg"
                onChange={(e) => setUploadedFile(e.target.files?.[0] || null)}
              />
              {editingAudio?.audio_url && !uploadedFile && (
                <audio
                  controls
                  src={toUrl(editingAudio.audio_url)}
                  className="mt-2 w-full"
                />
              )}
            </div>

            <div className="flex justify-end items-center">
              <button
                type="submit"
                className="bg-teal-700 text-white px-4 py-2 rounded-lg hover:bg-teal-600"
              >
                {editingAudio ? t("update") : t("save")}
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
};

export default DistractionAudio;