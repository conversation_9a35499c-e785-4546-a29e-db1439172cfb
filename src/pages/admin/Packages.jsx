/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axiosInstance from '../../utils/instance_axios';
import TableWidget from '../../widgets/TableWidget';
import { FaEdit } from 'react-icons/fa';
import { MdOutlineToggleOn, MdOutlineToggleOff } from 'react-icons/md';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';

const AdminSubscriptionPackages = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const [loading, setLoading] = useState(true);
  const [packages, setPackages] = useState([]);

  // modal + form
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPackage, setEditingPackage] = useState(null);
  const [formData, setFormData] = useState({ name: '', monthly_price: '', yearly_price: '' });

  const fetchPackages = async () => {
    try {
      const { data } = await axiosInstance.get('/subscriptions');
      setPackages(Array.isArray(data?.packages) ? data.packages : []);
    } catch (error) {
      console.error('Error fetching packages:', error?.message || error);
      toast.error(t('failed_to_fetch_packages'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(fetchPackages, 3000); // keep your splash delay
    return () => clearTimeout(timer);
  }, []);

  const togglePackageStatus = async (id, currentStatus) => {
    try {
      await axiosInstance.put(`/subscriptions/${id}/status`, { is_active: !currentStatus });
      toast.success(t('package_status_updated'));
      fetchPackages();
    } catch {
      toast.error(t('failed_to_update_package_status'));
    }
  };

  const openEditModal = (pkg) => {
    setEditingPackage(pkg);
    setFormData({
      name: pkg?.name ?? '',
      monthly_price: pkg?.monthly_price ?? '',
      yearly_price: pkg?.yearly_price ?? '',
    });
    setIsModalOpen(true);
  };

  const resetForm = () => {
    setEditingPackage(null);
    setFormData({ name: '', monthly_price: '', yearly_price: '' });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingPackage) {
        await axiosInstance.put(`/subscriptions/${editingPackage.id}`, formData);
        toast.success(t('package_updated_successfully'));
      } else {
        await axiosInstance.post('/subscriptions', formData);
        toast.success(t('package_created_successfully'));
      }
      setIsModalOpen(false);
      resetForm();
      fetchPackages();
    } catch (err) {
      console.error(err);
      toast.error(t('failed_to_create_package'));
    }
  };

  if (loading) return <ProgressBarWidget />;

  // ---- TableWidget (columns + rows) ----
  const columns = [
    {
      key: 'index',
      label: t('id'),
      align: 'center',
      minWidth: 70,
      render: (_value, _row, index) => index + 1,
    },
    { key: 'name', label: t('name'), align: 'center', minWidth: 200 },
    {
      key: 'monthly_price',
      label: t('monthly_price'),
      align: 'center',
      minWidth: 160,
      render: (value) => (value != null ? `$${value}` : '—'),
    },
    {
      key: 'yearly_price',
      label: t('yearly_price'),
      align: 'center',
      minWidth: 160,
      render: (value) => (value != null ? `$${value}` : '—'),
    },
    {
      key: 'is_active',
      label: t('status'),
      align: 'center',
      minWidth: 140,
      render: (value) => (value ? t('active') : t('inactive')),
    },
    {
      key: 'actions',
      label: t('actions'),
      align: 'center',
      minWidth: 300,
      render: (_value, row) => (
        <div className="flex gap-2 items-center justify-center">
          <button
            onClick={() => togglePackageStatus(row.id, row.is_active)}
            className={`p-2 rounded-md text-white ${row.is_active ? 'bg-teal-500' : 'bg-red-500'}`}
            title={t('toggle_status')}
          >
            {row.is_active ? <MdOutlineToggleOff size={22} /> : <MdOutlineToggleOn size={22} />}
          </button>

          <button
            className="p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            onClick={() => openEditModal(row)}
            title={t('edit')}
          >
            <FaEdit size={18} />
          </button>

          <button
            className="p-2 border-2 border-teal-600 bg-[#f5f5f5] text-teal-700 font-semibold rounded-md hover:bg-teal-50"
            onClick={() => navigate(`/dashboard/subscriptions/${row.id}/features`)}
          >
            {t('manage_features')}
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <TableWidget
        title={t('subscriptions')}
        locationNames={location.pathname}
        columns={columns}
        rows={packages}
        noDataMessage={t('no_data_available')}
        onAddClick={() => {
          resetForm();
          setIsModalOpen(true);
        }}
        vh={85}
        scrollX
        minTableWidth={980}
        clampLines={1}
        ellipsisTooltip
      />

      {/* Create / Edit Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 flex justify-center items-center">
          <div className="bg-white rounded-lg p-6 w-[90%] max-w-md">
            <h2 className="text-xl font-bold mb-4">
              {editingPackage ? t('edit_package') : t('create_package')}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <input
                type="text"
                placeholder={t('name')}
                value={formData.name}
                onChange={(e) => setFormData((p) => ({ ...p, name: e.target.value }))}
                className="w-full border p-2 rounded"
                required
              />
              <input
                type="number"
                placeholder={t('monthly_price')}
                value={formData.monthly_price}
                onChange={(e) => setFormData((p) => ({ ...p, monthly_price: e.target.value }))}
                className="w-full border p-2 rounded"
                required
              />
              <input
                type="number"
                placeholder={t('yearly_price')}
                value={formData.yearly_price}
                onChange={(e) => setFormData((p) => ({ ...p, yearly_price: e.target.value }))}
                className="w-full border p-2 rounded"
                required
              />

              <div className="flex justify-between gap-2">
                <button type="submit" className="px-4 py-2 bg-teal-600 text-white rounded-lg">
                  {editingPackage ? t('update') : t('create')}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setIsModalOpen(false);
                    resetForm();
                  }}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg"
                >
                  {t('cancel')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default AdminSubscriptionPackages;