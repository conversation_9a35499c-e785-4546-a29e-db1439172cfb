/* eslint-disable react-hooks/exhaustive-deps */
import { useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import axiosInstance from '../../utils/instance_axios';
import TableWidget from '../../widgets/TableWidget';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import ProgressBarWidget from '../../widgets/ProgresBarWidget'; // ✅ correct import name
import { CgTrash } from 'react-icons/cg';
import { FaEdit } from 'react-icons/fa';
import { MdArrowBackIos, MdOutlineToggleOff, MdOutlineToggleOn } from 'react-icons/md';
import { IoIosArrowDown } from 'react-icons/io';
import { AiOutlineClose } from 'react-icons/ai';
import { motion } from 'framer-motion';

const PackageFeatures = () => {
  const { package_id } = useParams();
  const { t } = useTranslation();

  const [features, setFeatures] = useState([]);
  const [packageName, setPackageName] = useState('');
  const [loading, setLoading] = useState(true);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingFeatureId, setEditingFeatureId] = useState(null);

  const [allFeatures, setAllFeatures] = useState([]);
  const [selectedFeatures, setSelectedFeatures] = useState([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  /* -------------------- API -------------------- */
  const fetchPackageName = async () => {
    try {
      const res = await axiosInstance.get(`/subscriptions/${package_id}`);
      setPackageName(res.data?.package?.name || '');
    } catch (error) {
      console.error(error);
      toast.error(t('failed_to_fetch_package_name'));
    }
  };

  const fetchFeatures = async () => {
    try {
      const res = await axiosInstance.get(`/subscriptions/${package_id}/features`);
      setFeatures(Array.isArray(res.data?.features) ? res.data.features : []);
    } catch (error) {
      console.error(error);
      toast.error(t('failed_to_fetch_features'));
    } finally {
      setLoading(false);
    }
  };

  const fetchAllFeatures = async () => {
    try {
      const res = await axiosInstance.get('/features');
      const list = Array.isArray(res.data?.features) ? res.data.features : [];
      setAllFeatures(list);
      // Clean any previously selected IDs that no longer exist
      setSelectedFeatures((prev) => prev.filter((id) => list.some((f) => f.id === id)));
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const timer = setTimeout(async () => {
      await Promise.all([fetchPackageName(), fetchFeatures(), fetchAllFeatures()]);
    }, 3000); // keep your splash delay
    return () => clearTimeout(timer);
  }, [package_id]);

  /* -------------------- Actions -------------------- */
  const navigateBack = () => window.history.back();

  const handleToggleStatus = async (id, currentStatus) => {
    try {
      await axiosInstance.put(`/subscriptions/features/${id}/status`, {
        is_active: !currentStatus,
      });
      toast.success(t('feature_status_updated'));
      fetchFeatures();
    } catch (error) {
      console.error(error);
      toast.error(t('failed_to_update_feature_status'));
    }
  };

  const handleDeleteFeature = async (id) => {
    try {
      await axiosInstance.delete(`/subscriptions/features/${id}`);
      toast.success(t('feature_deleted_successfully'));
      fetchFeatures();
    } catch (error) {
      console.error(error);
      toast.error(t('failed_to_delete_feature'));
    }
  };

  const openCreateModal = () => {
    setEditingFeatureId(null);
    setSelectedFeatures([]);
    setIsModalOpen(true);
  };

  const openEditModal = (featureRow) => {
    // featureRow contains: id (junction id), feature_id (the base feature id), feature_name, ...
    if (!featureRow?.feature_id) {
      toast.error(t('invalid_feature_selected'));
      return;
    }
    setEditingFeatureId(featureRow.id);
    setSelectedFeatures([featureRow.feature_id]);
    setIsModalOpen(true);
  };

  console.log('Selected Features:', features);

  const handleSubmitFeature = async (e) => {
    e.preventDefault();

    if (selectedFeatures.length === 0) {
      toast.error(t('please_select_feature'));
      return;
    }

    try {
      if (editingFeatureId) {
        const featureId = selectedFeatures[0];
        if (!featureId || typeof featureId !== 'number') {
          toast.error(t('invalid_feature_selected'));
          return;
        }
        await axiosInstance.put(`/subscriptions/features/${editingFeatureId}`, {
          feature_id: featureId,
        });
        toast.success(t('feature_updated_successfully'));
      } else {
        // add multiple at once
        await Promise.all(
          selectedFeatures.map((featureId) =>
            axiosInstance.post(`/subscriptions/${package_id}/features`, {
              feature_id: featureId,
            })
          )
        );
        toast.success(t('feature_added_successfully'));
      }

      setIsModalOpen(false);
      setEditingFeatureId(null);
      setSelectedFeatures([]);
      fetchFeatures();
    } catch (error) {
      console.error(error);
      const errorMessage =
        error?.response?.data?.error ||
        (editingFeatureId ? t('failed_to_update_feature') : t('failed_to_add_feature'));
      toast.error(errorMessage);
    }
  };

  /* -------------------- Table (new API) -------------------- */
  const columns = [
    {
      key: 'index',
      label: t('id'),
      align: 'center',
      minWidth: 80,
      render: (_value, _row, index) => index + 1,
    },
    {
      key: 'feature_name',
      label: t('name'),
      align: 'center',
      minWidth: 200,
    },
    {
      key: 'feature_discount_percentage',
      label: t('discount_percentage'),
      align: 'center',
      minWidth: 150,
      render: (value) => (value ? `${value}%` : '-'),
    },
    {
      key: 'feature_type',
      label: t('type'),
      align: 'center',
      minWidth: 150,
      render: (value) => {
        <p className="text-sm text-gray-600">
          {value}
        </p>
      }
    },
    {
      key: 'is_active',
      label: t('status'),
      align: 'center',
      minWidth: 140,
      render: (value) => (value ? t('active') : t('inactive')),
    },
    
    {
      key: 'actions',
      label: t('actions'),
      align: 'center',
      minWidth: 260,
      render: (_value, row) => (
        <div className="flex items-center gap-2 justify-center">
          <button
            onClick={() => handleToggleStatus(row.id, row.is_active)}
            className={`p-2 rounded-md text-white ${row.is_active ? 'bg-teal-600' : 'bg-red-500'}`}
            title={t('toggle_status')}
          >
            {row.is_active ? <MdOutlineToggleOff size={22} /> : <MdOutlineToggleOn size={22} />}
          </button>

          <button
            className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-500"
            onClick={() => openEditModal(row)}
            title={t('edit')}
          >
            <FaEdit size={18} />
          </button>

          <button
            className="p-2 bg-red-600 text-white rounded-md hover:bg-red-500"
            onClick={() => handleDeleteFeature(row.id)}
            title={t('delete')}
          >
            <CgTrash size={18} />
          </button>
        </div>
      ),
    },
  ];

  if (loading) return <ProgressBarWidget />;

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">
          {t('package_features')} — {packageName || `ID: ${package_id}`}
        </h2>

        <button
          onClick={navigateBack}
          className="text-sm bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded-md transition"
        >
          {t('back_to_packages')} <MdArrowBackIos size={16} className="inline" />
        </button>
      </div>

      <TableWidget
        title={t('features')}
        columns={columns}
        rows={features}
        noDataMessage={t('no_data_available')}
        onAddClick={openCreateModal}
        vh={80}
        scrollX
        minTableWidth={800}
        clampLines={1}
        ellipsisTooltip
      />

      {/* Add / Edit Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-[90%] max-w-md">
            <form
              onSubmit={handleSubmitFeature}
              onClick={() => {
                if (dropdownOpen) setDropdownOpen(false);
              }}
            >
              <h3 className="text-lg font-bold mb-4">
                {editingFeatureId ? t('edit_feature') : t('add_feature')}
              </h3>

              {/* Feature multi-select (chips + dropdown) */}
              <div className="relative w-full mt-2">
                <label className="block mb-1 text-sm md:text-base font-medium text-gray-700">
                  {t('choose_feature')}
                </label>

                <div
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm bg-white cursor-pointer flex flex-wrap gap-2 min-h-[42px]"
                  onClick={(e) => {
                    e.stopPropagation();
                    setDropdownOpen((prev) => !prev);
                  }}
                >
                  {selectedFeatures.length > 0 ? (
                    selectedFeatures.map((featId) => {
                      const f = allFeatures.find((x) => x.id === featId);
                      if (!f) return null;
                      return (
                        <motion.div
                          key={featId}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded-full flex items-center gap-1 shadow-sm"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {f.name}
                          <button
                            type="button"
                            className="hover:text-red-500"
                            onClick={() =>
                              setSelectedFeatures((prev) => prev.filter((id) => id !== featId))
                            }
                          >
                            <AiOutlineClose size={14} />
                          </button>
                        </motion.div>
                      );
                    })
                  ) : (
                    <div className="flex justify-between items-center w-full">
                      <span className="text-gray-400">{t('select_feature_placeholder')}</span>
                      <IoIosArrowDown className="text-gray-500" />
                    </div>
                  )}
                </div>

                {dropdownOpen && (
                  <motion.ul
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow mt-1 max-h-60 overflow-auto"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {allFeatures.map((feat) => {
                      const active = selectedFeatures.includes(feat.id);
                      return (
                        <li
                          key={feat.id}
                          className={`px-4 py-2 text-sm hover:bg-teal-100 hover:text-teal-800 cursor-pointer ${
                            active ? 'bg-teal-500 text-white font-semibold' : ''
                          }`}
                          onClick={() =>
                            setSelectedFeatures((prev) =>
                              active ? prev.filter((id) => id !== feat.id) : [...prev, feat.id]
                            )
                          }
                        >
                          {feat.name}
                        </li>
                      );
                    })}
                  </motion.ul>
                )}
              </div>

              <div className="flex items-center justify-between gap-2 mt-6">
                <button
                  type="button"
                  className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded"
                  onClick={() => {
                    setIsModalOpen(false);
                    setSelectedFeatures([]);
                    setEditingFeatureId(null);
                  }}
                >
                  {t('cancel')}
                </button>
                <button type="submit" className="px-4 py-2 bg-teal-600 hover:bg-teal-500 text-white rounded">
                  {t('save')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default PackageFeatures;