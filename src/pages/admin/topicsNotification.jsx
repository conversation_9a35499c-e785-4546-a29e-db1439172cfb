/* eslint-disable react-hooks/exhaustive-deps */
import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import { FaEdit, FaPlus, FaMinus } from "react-icons/fa";
import axiosInstance from "../../utils/instance_axios";
import { useParams } from "react-router-dom";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";
import { CgTrash, CgClose } from "react-icons/cg";

const TopicsNotification = () => {
  const { id, name } = useParams();
  const { t } = useTranslation();

  const [notification, setNotification] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const [modalOpen, setModalOpen] = useState(false);
  const [titles, setTitles] = useState([""]); // for create (multi) / edit (single at [0])
  const [editingId, setEditingId] = useState(null);

  const fetchNotification = async () => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.get(`/topics/${id}/notifications`);
      const data = response?.data?.data;
      setNotification(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching notification:", error.response?.data || error.message);
      setNotification([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) fetchNotification();
  }, [id]);

  const handleDelete = async (notifId) => {
    try {
      await axiosInstance.delete(`/topics/delete/${notifId}`);
      setNotification((prev) => prev.filter((n) => n.id !== notifId));
    } catch (error) {
      console.error("Error deleting notification:", error.message);
    }
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    try {
      if (!editingId || titles[0].trim() === "") return;
      await axiosInstance.put(`/topics/edit/${editingId}`, { text: titles[0] });
      setEditingId(null);
      setModalOpen(false);
      setTitles([""]);
      fetchNotification();
    } catch (error) {
      console.error("Error editing notification:", error.message);
    }
  };

  const handleCreateSubmit = async (e) => {
    e.preventDefault();
    const validTitles = titles.map((s) => s.trim()).filter(Boolean);
    if (validTitles.length === 0) return;

    try {
      // create many
      for (const title of validTitles) {
        await axiosInstance.post(`/topics/create-notification/${id}`, { text: title });
      }
      setModalOpen(false);
      setTitles([""]);
      fetchNotification();
    } catch (error) {
      console.error("Error creating notification:", error.response?.data || error.message);
    }
  };

  const handleAddInput = () => setTitles((prev) => [...prev, ""]);
  const handleRemoveInput = (index) => {
    setTitles((prev) => (prev.length === 1 ? prev : prev.filter((_, i) => i !== index)));
  };
  const handleChange = (index, value) => {
    setTitles((prev) => {
      const next = [...prev];
      next[index] = value;
      return next;
    });
  };

  const openCreateModal = () => {
    setEditingId(null);
    setTitles([""]);
    setModalOpen(true);
  };

  const openEditModal = (row) => {
    setEditingId(row.id);
    setTitles([typeof row.text === "string" ? row.text : JSON.stringify(row.text)]);
    setModalOpen(true);
  };

  const closeModal = () => {
    setEditingId(null);
    setTitles([""]);
    setModalOpen(false);
  };

  // ---------- NEW TableWidget columns/rows ----------
  const columns = [
    {
      key: "id",
      label: t("id"),
      align: "center",
      render: (_value, _row, index) => index + 1,
      width: 80,
    },
    {
      key: "text",
      label: t("title"),
      align: "center",
      render: (value) => (
        <div className="max-w-[600px] mx-auto text-center whitespace-nowrap overflow-hidden text-ellipsis" title={typeof value === "string" ? value : JSON.stringify(value)}>
          {typeof value === "string" ? value : JSON.stringify(value)}
        </div>
      ),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      width: 160,
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            type="button"
            className="flex items-center bg-teal-700 text-white p-2 rounded-lg hover:bg-teal-600 transition"
            onClick={() => openEditModal(row)}
            title={t("edit")}
          >
            <FaEdit size={20} />
          </button>
          <button
            type="button"
            className="flex items-center bg-red-700 text-white p-2 rounded-lg hover:bg-red-600 transition"
            onClick={() => handleDelete(row.id)}
            title={t("delete")}
          >
            <CgTrash size={20} />
          </button>
        </div>
      ),
    },
  ];

  if (isLoading) return <ProgressBarWidget />;

  return (
    <>
      <TableWidget
        title={t("notifications")}
        subtitle={name ? `${t("topic")}: ${name}` : undefined}
        columns={columns}
        rows={notification}
        noDataMessage={t("no_data_available")}
        isAddClick
        onAddClick={openCreateModal}
        vh={90}
      />

      {modalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white p-6 rounded shadow-lg w-[700px] relative">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">
                {editingId ? t("edit_notification") : t("create_notification")}
              </h2>
              <div className="flex items-center gap-2">
                {!editingId && (
                  <button
                    type="button"
                    className="bg-teal-700 text-white px-3 py-2 rounded-lg hover:bg-teal-600 transition"
                    onClick={handleAddInput}
                    title={t("add")}
                  >
                    <FaPlus />
                  </button>
                )}
                <button
                  className="text-white bg-red-700 hover:bg-red-600 p-1 rounded-full"
                  onClick={closeModal}
                  title={t("close")}
                >
                  <CgClose size={20} />
                </button>
              </div>
            </div>

            <form onSubmit={editingId ? handleEditSubmit : handleCreateSubmit}>
              <div
                className={`${
                  titles.length > 4 ? "h-[230px] overflow-y-auto" : editingId ? "h-[40px] overflow-y-auto" : ""
                } w-full mb-8`}
              >
                {titles.map((title, index) => (
                  <div key={index} className="mb-4 flex items-center gap-2">
                    <input
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      type="text"
                      placeholder={t("enter_notification_message")}
                      value={title}
                      onChange={(e) => handleChange(index, e.target.value)}
                      required
                    />
                    {!editingId && titles.length > 1 && (
                      <button
                        type="button"
                        className="bg-red-700 text-white px-2 py-1 rounded-lg hover:bg-red-600 transition"
                        onClick={() => handleRemoveInput(index)}
                        title={t("remove")}
                      >
                        <FaMinus />
                      </button>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex items-center justify-end">
                <button
                  className="bg-teal-700 hover:bg-teal-600 text-white font-bold py-2 px-4 rounded"
                  type="submit"
                >
                  {t("save")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default TopicsNotification;