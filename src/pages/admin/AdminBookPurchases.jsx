import { useEffect, useState } from "react";
import axiosInstance from "../../utils/instance_axios";
import TableWidget from "../../widgets/TableWidget";
import ProgresBarWidget from "../../widgets/ProgresBarWidget";
import { useTranslation } from "react-i18next";

const AdminBookPurchases = () => {
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const { t, i18n } = useTranslation();

  const fetchPurchases = async () => {
    try {
      const response = await axiosInstance.get("/purchase-books/admin/purchases");
      setPurchases(response.data.purchases || []);
      console.log("Purchases:", response.data.purchases);
    } catch (err) {
      console.error("Error fetching purchases:", err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchPurchases();
    }, 1000);
    return () => clearTimeout(timeout);
  }, []);

  const columns = [
    { key: "index", label: t("id"), align: "center" },
    { key: "full_name", label: t("name"), align: "right" },
    { key: "email", label: t("email"), align: "right" },
    { key: "book_name", label: t("book_name"), align: "right" },
    { key: "price", label: t("price"), align: "center" },
    { key: "purchased_at", label: t("purchased_at"), align: "center" },
  ];

  const rows = purchases.map((purchase, index) => ({
    index: index + 1,
    full_name: purchase.full_name,
    email: purchase.email,
    book_name: purchase.book_name,
    price: `$${purchase.price}`,
    purchased_at: new Date(purchase.purchased_at).toLocaleString(
      i18n.language === "ar" ? "ar-EG" : i18n.language === "he" ? "he-IL" : "en-US"
    ),
  }));

  if (loading) {
    return <ProgresBarWidget />;
  }

  return (
    <TableWidget
      title={t("purchase_history")}
      columns={columns} // ✅ fixed this
      rows={rows}
      locationNames="/admin/purchases"
      noDataMessage={t("no_data_available")}
      isAddClick={false}
    />
  );
};

export default AdminBookPurchases;