import { useEffect, useState } from 'react';
import { useParams, NavLink } from 'react-router-dom';
import axiosInstance, { baseURL } from '../../utils/instance_axios';
import { toast } from 'react-toastify';
import ProgressBarWidget from '../../widgets/ProgresBarWidget';

const BookDetailsCard = () => {
  const { bookId } = useParams();

  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBookDetails = async () => {
      try {
        const response = await axiosInstance.get(`/books/${bookId}`);
        const bookData = response.data.data;

        if (!bookData) {
          toast.error('No data available for this book.');
          return;
        }

        setBook(bookData);
      } catch (error) {
        console.error('Error fetching book details:', error);
        toast.error('Failed to fetch book details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchBookDetails();
  }, [bookId]);

  if (loading) {
    return (
      <ProgressBarWidget />
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-2">
      <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg overflow-hidden p-4">
        <div className="flex flex-col md:flex-row">
          {/* Book Cover Image */}
          <div className="md:w-1/3 mb-4 md:mb-0">
            <img
              src={`${baseURL}${book.image}`}
              alt={book.name}
              className="w-full h-full object-cover rounded-md"
            />
          </div>

          {/* Book Details */}
          <div className="md:w-2/3 p-6 flex flex-col">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{book.name}</h1>
            <p className="text-gray-600 mb-4">{book.description}</p>
            <div className="flex flex-wrap mb-4">
              <div className="w-full md:w-1/2 mb-2">
                <h2 className="text-sm font-semibold text-gray-600">Price:</h2>
                <p className="text-lg text-gray-800">₪{book.original_price}</p>
              </div>
              <div className="w-full md:w-1/2 mb-2">
                <h2 className="text-sm font-semibold text-gray-600">Pages:</h2>
                <p className="text-lg text-gray-800">{book.pages?.length || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <NavLink
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-all duration-300"
                to={`/dashboard/preview/${bookId}`}
              >
                Start Reading
              </NavLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookDetailsCard;
