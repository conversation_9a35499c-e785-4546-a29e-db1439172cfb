import { useEffect, useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import Topbar from './Topbar';
import { useTranslation } from 'react-i18next';

const Admin = () => {
  const navigate = useNavigate();
  const { i18n } = useTranslation();

  // Define the search state
  const [search, setSearch] = useState('');

  // Authentication and role verification
  useEffect(() => {
    const token = localStorage.getItem('token');
    const role = localStorage.getItem('role');

    if (!token || !role.includes('admin')) {
      navigate('/login'); // Redirect if not authenticated or not admin
    }
  }, [navigate]);

  // Check if the current language is RTL
  const isRtl = i18n.language === 'ar' || i18n.language === 'he';

  // Handle search logic
  const handleSearch = (searchTerm) => {
    setSearch(searchTerm);
    console.log('Search Term:', searchTerm);
  };

  return (
    <div className="flex h-screen" style={{ overflow: 'hidden' }} dir={isRtl ? 'rtl' : 'ltr'}>
      {/* Sidebar */}
      <Sidebar isRtl={isRtl} />
      <div className="flex-1 flex flex-col">
        {/* Topbar */}
        <Topbar onSearch={handleSearch} isRtl={isRtl} />
        <div className="p-4 flex-1" style={{ overflow: 'auto' }}>
          {/* Pass search term to child routes */}
          <Outlet context={{ search }} style={{ overflow: 'auto' }} />
        </div>
      </div>
    </div>
  );
};

export default Admin;
