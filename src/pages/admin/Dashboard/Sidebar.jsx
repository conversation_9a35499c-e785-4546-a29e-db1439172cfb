/* eslint-disable react/prop-types */
import { useTranslation } from 'react-i18next';
import { useLocation, matchPath } from 'react-router-dom';
import {
  MdDashboard,
  MdPeople,
  MdBook,
  MdSubscriptions,
  MdVideoLibrary,
  MdQuestionAnswer,
  MdOutlineLibraryBooks,
  MdOutlineSupportAgent,
  MdOutlineReport,
  MdOutlineNotifications,
  MdSelfImprovement,
  MdPsychology,
} from 'react-icons/md';
import { FaShoppingCart } from 'react-icons/fa';
import { RiMentalHealthFill } from 'react-icons/ri';
import TopBarLogo from '../../../assets/arabSBT_icon.png';
import Nav from "../../../widgets/navLink";


const Sidebar = ({ isRtl, isMobile }) => {
  const { t } = useTranslation();
  const { pathname } = useLocation();

  // פונקציה שמאפשרת לבדוק אם הנתיב תואם לאחד מהרשימה, עם שליטה על end
  const matchAny = (patterns, exact = false) =>
    patterns.some((pattern) =>
      matchPath({ path: pattern, end: exact }, pathname)
    );

  



  return (
    <aside
      className={`${isMobile ? 'hidden' : 'w-[280px]'} text-white h-screen bg-gray-800 border-r border-gray-200 shadow-md flex flex-col`}
      dir={isRtl ? 'rtl' : 'ltr'}
    >
      {/* Logo */}
      <div className="flex items-center justify-center gap-2 py-5 border-b border-gray-100">
        <img src={TopBarLogo} alt="Logo" className="w-[38px] h-[38px] text-white" />
        <h1 className="text-xl font-bold tracking-wide text-blue-600">
          Arab<span className="text-white">CBT</span>
        </h1>
      </div>

      <nav className="flex-1 overflow-y-auto px-2 py-3 space-y-1">
        <Nav
          to="/dashboard"
          icon={<MdDashboard size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
          exact={true} // חשוב: התאמה מדויקת כדי שלא יפעל על /dashboard/*
        >
          {t('home')}
        </Nav>

        <Nav
          to="/dashboard/users"
          isActive={matchAny(['/dashboard/users'])}
          icon={<MdPeople size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('users')}
        </Nav>



        <Nav
          to="/dashboard/topics"
          isActive={matchAny(['/dashboard/topics'])}
          icon={<MdOutlineNotifications size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('notifications')}
        </Nav>



        <Nav
          to="/dashboard/books"
          isActive={matchAny([
            '/dashboard/books',
            '/dashboard/pages',
            '/dashboard/add-pages',
            '/dashboard/preview/:id',
          ])}
          icon={<MdBook size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('books')}
        </Nav>

        <Nav
          to="/dashboard/book-purchases"
          isActive={matchAny(['/dashboard/book-purchases'])}
          icon={<FaShoppingCart size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('book_purchases')}
        </Nav>



        <Nav
          to="/dashboard/concerns"
          childrenLinks={[
            "/dashboard/thoughts",
            "/dashboard/questions"
          ]}
          icon={<MdQuestionAnswer size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {pathname.includes("/dashboard/concerns")
            ? t("concerns")
            : pathname.includes("/dashboard/questions")
              ? t("questions")
              : t("thoughts")}
        </Nav>

        <Nav
          to="/dashboard/online-treatment"
          isActive={matchAny([
            '/dashboard/online-treatment',
            '/dashboard/online-treatment/treatment-video/:id',
          ])}
          icon={<MdVideoLibrary size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('video_treatment')}
        </Nav>

        <Nav
          to="/dashboard/lectures"
          isActive={matchAny(['/dashboard/lectures'])}
          icon={<MdOutlineLibraryBooks size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('lectures')}
        </Nav>



        <Nav
          to="/dashboard/distractions"
          isActive={matchAny(['/dashboard/distractions'])}
          icon={<MdPsychology size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('distractions_activities')}
        </Nav>

        <Nav
          to="/dashboard/meditation"
          isActive={matchAny([
            '/dashboard/meditation',
            '/dashboard/meditation/audio/:id',
          ])}
          icon={<MdSelfImprovement size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('meditation')}
        </Nav>



        <Nav
          to="/dashboard/subscriptions"
          isActive={matchAny([
            '/dashboard/subscriptions',
            '/dashboard/subscriptions/:package_id/features',
          ])}
          icon={<MdSubscriptions size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('subscriptions')}
        </Nav>

        <Nav
          to="/dashboard/features"
          isActive={matchAny(['/dashboard/features'])}
          icon={<RiMentalHealthFill size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('features')}
        </Nav>

        <Nav
          to="/dashboard/reports"
          isActive={matchAny(['/dashboard/reports'])}
          icon={<MdOutlineReport size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('reports')}
        </Nav>

        <Nav
          to="/dashboard/support"
          isActive={matchAny(['/dashboard/support'])}
          icon={<MdOutlineSupportAgent size={isMobile ? 18 : 24} />}
          isRtl={isRtl}
        >
          {t('support')}
        </Nav>
      </nav>
    </aside>
  );
};

export default Sidebar;