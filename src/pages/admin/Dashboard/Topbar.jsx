import { useEffect, useRef, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { FaUserCircle, FaSignOutAlt, FaGlobe } from 'react-icons/fa';
import { IoMdArrowDropdown } from "react-icons/io";
import PropTypes from 'prop-types';
import { useUser } from '../../../utils/UserContext';
import { useTranslation } from 'react-i18next';
import Logo from '../../../assets/arab_cbt_logo.png';

const Topbar = ({ isRtl }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { handleLogout, user } = useUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null); // ⬅️ Reference to dropdown

  const token = localStorage.getItem('token');

  useEffect(() => {
    if (!token) navigate('/login');
  }, [token, navigate]);

  const handleUserLogout = () => {
    handleLogout();
    localStorage.clear();
    navigate('/login');
  };

  const username = user?.name ?? 'User';

  // ⬇️ Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };

    if (dropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  return (
    <header className={`w-full h-[77px] px-6 bg-white shadow-md border-b flex items-center justify-between ${isRtl ? 'flex-row-reverse' : 'flex-row'}`}>
      
      {token && (
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setDropdownOpen(prev => !prev)}
            className="flex items-center gap-3 hover:bg-gray-100 p-2 rounded-lg transition-all duration-300 outline-none"
          >
            <img src={Logo} alt="Logo" className="w-9 h-9 rounded-full border border-gray-300" />
            <span className="font-semibold text-gray-800">{username}</span>
            <IoMdArrowDropdown size={20} className="text-gray-600" />
          </button>

          {dropdownOpen && (
            <div className={`absolute top-[52px] ${isRtl ? 'left-0' : 'right-0'} w-60 bg-white border shadow-lg rounded-lg z-50 animate-fade-in`}>
              <NavLink
                to="/dashboard/profile"
                onClick={() => setDropdownOpen(false)}
                className="flex items-center gap-3 px-4 py-3 hover:bg-blue-100 text-gray-800 transition rounded-t-lg"
              >
                <FaUserCircle className="text-blue-500" />
                <span>{t('profile')}</span>
              </NavLink>
              <NavLink
                to="/home"
                onClick={() => setDropdownOpen(false)}
                className="flex items-center gap-3 px-4 py-3 hover:bg-blue-100 text-gray-800 transition"
              >
                <FaGlobe className="text-green-600" />
                <span>{t('my_website')}</span>
              </NavLink>
              <button
                onClick={handleUserLogout}
                className="flex items-center gap-3 px-4 py-3 w-full text-left hover:bg-red-100 text-red-600 transition rounded-b-lg"
              >
                <FaSignOutAlt />
                <span>{t('logout')}</span>
              </button>
            </div>
          )}
        </div>
      )}
    </header>
  );
};

Topbar.propTypes = {
  isRtl: PropTypes.bool.isRequired,
};

export default Topbar;