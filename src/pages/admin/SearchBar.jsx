// SearchBar.jsx
import { useState, useEffect } from 'react';
import { BiSearchAlt2 } from "react-icons/bi";
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next'; 

const SearchBar = ({ onSearch, isRtl }) => {
  const { t, i18n } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [isHovered, setIsHovered] = useState(false);
  const [isTranslationLoaded, setIsTranslationLoaded] = useState(false);

  useEffect(() => {
    if (i18n.isInitialized) {
      setIsTranslationLoaded(true);
    } else {
      i18n.on('initialized', () => setIsTranslationLoaded(true));
    }
  }, [i18n]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    if (e.target.value !== '') {
      setIsHovered(true); // Keep the form open when there is input
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    onSearch(searchTerm);
    setSearchTerm(''); // Optionally clear the input after search
    setIsHovered(false); // Close the form after submission if you want
  };

  const handleMouseLeave = () => {
    if (searchTerm === '') {
      setIsHovered(false); // Close only if the search term is empty
    }
  };

  if (!isTranslationLoaded) return null; // Wait until translations are loaded

  return (
    <form 
      onSubmit={handleSearchSubmit} 
      className="flex items-center w-full relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave} // Adjusted to keep input open if there is text
    >
      <div className={`flex items-center w-full rounded-full border-none transition-all duration-500 ${isHovered ? 'shadow-lg bg-[#F5F5F5]' : ''}`}>
        <button
          type="submit"
          className={`p-2 text-white bg-[#51B4E2] rounded-full transition-all duration-500 ${isHovered ? 'mr-0' : 'mr-2'}`}
        >
          <BiSearchAlt2 size={25} />
        </button>
        <input
          type="text"
          value={searchTerm}
          onChange={handleSearchChange}
          placeholder={t("search")}
          className={`p-2 text-[#51B4E2] bg-transparent transition-all duration-500 focus:outline-none ${isHovered ? 'w-full opacity-100' : 'w-0 opacity-0'}`}
          dir={isRtl ? 'rtl' : 'ltr'}
          style={{
            minWidth: isHovered ? '150px' : '0px'
          }}
        
        />
      </div>
    </form>
  );
};

SearchBar.propTypes = {
  onSearch: PropTypes.func.isRequired,
  isRtl: PropTypes.bool.isRequired,
};

export default SearchBar;
