/* eslint-disable react/prop-types */

import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import axiosInstance from "../../utils/instance_axios";
import { FaEdit } from "react-icons/fa";
import { MdDelete, MdPassword } from "react-icons/md";
import toast from "react-hot-toast";
import { CgClose, CgEye, CgEyeAlt } from "react-icons/cg";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

/* ------------------------- User Edit Modal ------------------------- */
const UserModal = ({ openModal, setOpenModal, user, Submit }) => {
  const { t } = useTranslation();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    gender: "male",
    age: "",
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || "",
        email: user.email || "",
        gender: user.gender || "male",
        age: user.age || "",
      });
    } else {
      setFormData({ name: "", email: "", gender: "male", age: "" });
    }
  }, [user]);

  const handleChange = (field) => (e) =>
    setFormData((p) => ({ ...p, [field]: e.target.value }));

  const handleSubmit = (e) => {
    e.preventDefault();
    Submit(formData);
  };

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4 ${
        openModal ? "block" : "hidden"
      }`}
    >
      <div className="relative w-full max-w-lg mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-xl font-semibold text-gray-800">
            {t("edit_user")}
          </h2>
          <button
            onClick={() => setOpenModal(false)}
            className="p-2 rounded-full text-lg text-red-600 hover:text-white hover:bg-red-500 transition"
            aria-label="Close"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="2"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-5">
          <div>
            <label className="block mb-1 font-medium text-gray-700">
              {t("name")}
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={handleChange("name")}
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-400 focus:outline-none"
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">
              {t("email")}
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={handleChange("email")}
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:outline-none"
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">
              {t("gender")}
            </label>
            <div className="flex gap-2">
              {["male", "female"].map((gender) => (
                <button
                  key={gender}
                  type="button"
                  onClick={() => setFormData((p) => ({ ...p, gender }))}
                  className={`flex-1 px-4 py-2 border rounded-md transition focus:ring-2 focus:ring-teal-600 focus:outline-none
                    ${
                      formData.gender === gender
                        ? "bg-teal-600 text-white border-teal-600 hover:bg-teal-500"
                        : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
                    }`}
                >
                  {t(gender)}
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">
              {t("age")}
            </label>
            <input
              type="number"
              min="1"
              value={formData.age}
              onChange={handleChange("age")}
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-400 focus:outline-none"
            />
          </div>

          <div className="flex justify-end gap-3 pt-2">
            <button
              type="submit"
              className="px-5 py-2 bg-teal-600 text-white font-medium rounded-lg hover:bg-teal-500 transition"
            >
              {t("save")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

/* --------------------- Change Password Modal ---------------------- */
const ChangePasswordModal = ({ openModal, setOpenModal, user }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const togglePasswordVisibility = (field) =>
    setShowPassword((p) => ({ ...p, [field]: !p[field] }));

  const handleChange = (field) => (e) =>
    setFormData((p) => ({ ...p, [field]: e.target.value }));

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!user?.id) {
      toast.error("User information not available");
      return;
    }
    const { currentPassword, newPassword, confirmPassword } = formData;
    if (newPassword !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }
    try {
      const res = await axiosInstance.put(`/auth/${user.id}/change-password`, {
        currentPassword,
        newPassword,
      });
      if (res.status === 200) {
        toast.success("Password changed successfully");
        setOpenModal(false);
      } else {
        toast.error("Failed to change password");
      }
    } catch (error) {
      const msg = error?.response?.data?.message || "Error changing password";
      toast.error(msg);
    }
  };

  const onlyEnglishInputProps = {
    pattern: "[ -~]*",
    onInput: (e) => {
      e.target.value = e.target.value.replace(/[^\x20-\x7E]/g, "");
    },
  };

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center z-50 bg-black/50 ${
        openModal ? "visible" : "invisible"
      }`}
    >
      <div className="relative bg-white p-6 rounded-lg shadow-lg w-[400px]" dir="rtl">
        <div className="absolute top-2 left-2">
          <button
            className="bg-red-600 text-white p-1 rounded-full"
            onClick={() => setOpenModal(false)}
          >
            <CgClose size={20} />
          </button>
        </div>

        <h2 className="text-xl font-semibold text-gray-800">{t("change_password")}</h2>

        <form onSubmit={handleSubmit} className="p-6 space-y-3">
          <input
            type="text"
            name="username"
            autoComplete="username"
            defaultValue={user?.email || ""}
            readOnly
            className="hidden"
          />

          {/* Current */}
          <div className="relative">
            <label className="block mb-1 font-medium text-gray-700">{t("current_password")}</label>
            <input
              type={showPassword.current ? "text" : "password"}
              value={formData.currentPassword}
              onChange={handleChange("currentPassword")}
              autoComplete="current-password"
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:ring-2 focus:ring-teal-500 focus:outline-none"
              {...onlyEnglishInputProps}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility("current")}
              className="absolute left-3 top-[38px] text-gray-500 hover:text-gray-700"
            >
              {showPassword.current ? <CgEye /> : <CgEyeAlt />}
            </button>
          </div>

          {/* New */}
          <div className="relative">
            <label className="block mb-1 font-medium text-gray-700">{t("new_password")}</label>
            <input
              type={showPassword.new ? "text" : "password"}
              value={formData.newPassword}
              onChange={handleChange("newPassword")}
              autoComplete="new-password"
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:ring-2 focus:ring-teal-500 focus:outline-none"
              {...onlyEnglishInputProps}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility("new")}
              className="absolute left-3 top-[38px] text-gray-500 hover:text-gray-700"
            >
              {showPassword.new ? <CgEye /> : <CgEyeAlt />}
            </button>
          </div>

          {/* Confirm */}
          <div className="relative">
            <label className="block mb-1 font-medium text-gray-700">{t("confirm_password")}</label>
            <input
              type={showPassword.confirm ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={handleChange("confirmPassword")}
              autoComplete="new-password"
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:ring-2 focus:ring-teal-500 focus:outline-none"
              {...onlyEnglishInputProps}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility("confirm")}
              className="absolute left-3 top-[38px] text-gray-500 hover:text-gray-700"
            >
              {showPassword.confirm ? <CgEye /> : <CgEyeAlt />}
            </button>
          </div>

          <div className="flex justify-end gap-3 pt-2">
            <button
              type="submit"
              className="px-5 py-2 bg-teal-600 text-white font-medium rounded-lg hover:bg-teal-500 transition"
            >
              {t("save")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

/* ------------------------------ Users ----------------------------- */
const Users = () => {
  const { t } = useTranslation();
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [openChangePasswordModal, setOpenChangePasswordModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const onResize = () => setIsMobile(window.innerWidth < 768);
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.get("/auth");
      setUsers(response.data?.data || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error(t("something_went_wrong"));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const deleteUser = async (id) => {
    try {
      await axiosInstance.delete(`/auth/delete/${id}`);
      toast.success(t("user_deleted"));
      fetchUsers();
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error(t("error_deleting_user"));
    }
  };

  const toggleUserStatus = async (userId, currentStatus) => {
    try {
      const { data } = await axiosInstance.put(`/auth/toggle-status/${userId}`, {
        is_active: !currentStatus,
      });
      if (data?.success) {
        toast.success(
          t("status_updated", { status: data.newStatus ? t("active") : t("inactive") })
        );
        fetchUsers();
      } else {
        toast.error(t("failed_to_update_status"));
      }
    } catch (error) {
      console.error("Error toggling user status:", error);
      toast.error(t("error_updating_status"));
    }
  };

  // ---------- Columns (render(value, row, idx)) ----------
  const columns = [
    { key: "name", label: t("name"), align: "center" },

    {
      key: "email",
      label: t("email"),
      align: "center",
      render: (value, row) => (
        <div className="flex items-center justify-center max-w-[240px] w-full mx-auto truncate">
          {value ?? row?.email ?? ""}
        </div>
      ),
    },

    {
      key: "role",
      label: t("role"),
      align: "center",
      // ⬅️ FIX: use (value, row) signature
      render: (_value, row) => {
        const roles = row?.roles;
        const toLabel = (r) => {
          if (!r) return "";
          if (typeof r === "string") return t(r);
          if (typeof r === "object")
            return t(r.name || r.role || String(r.id || "role"));
          return String(r);
        };

        let content;
        if (Array.isArray(roles)) {
          content = roles.map((r, idx) => (
            <span key={idx}>
              {toLabel(r)}
              {idx < roles.length - 1 ? ", " : ""}
            </span>
          ));
        } else {
          content = toLabel(roles);
        }

        return (
          <div className="flex items-center justify-center max-w-[240px] w-full mx-auto truncate">
            {content}
          </div>
        );
      },
    },

    {
      key: "is_active",
      label: t("status"),
      align: "center",
      render: (value, row) => (
        <label
          className="relative inline-flex items-center cursor-pointer"
          title={value ? t("active") : t("inactive")}
        >
          <input
            type="checkbox"
            checked={!!value}
            onChange={() => toggleUserStatus(row.id, !!value)}
            className="sr-only peer"
            aria-label={t("status")}
          />
          <div className="w-11 h-6 bg-red-500 rounded-full peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-400 peer-checked:bg-teal-500 transition-all duration-300" />
          <span className="absolute left-1 top-1 w-4 h-4 bg-white rounded-full shadow peer-checked:translate-x-5 transform transition-transform duration-300" />
        </label>
      ),
    },

    {
      key: "actions",
      label: t("actions"),
      align: "center",
      render: (_value, row) => (
        <div
          className={`flex gap-2 ${
            isMobile ? "text-[16px]" : "text-[20px]"
          } justify-center`}
        >
          <button
            onClick={() => {
              setSelectedUser(row);
              setOpenModal(true);
            }}
            className="text-white bg-teal-500 p-1 rounded-md hover:bg-teal-600"
            title={t("edit")}
          >
            <FaEdit />
          </button>
          <button
            onClick={() => {
              setSelectedUser(row);
              setOpenChangePasswordModal(true);
            }}
            className="text-white bg-teal-500 p-1 rounded-md hover:bg-teal-600"
            title={t("change_password")}
          >
            <MdPassword />
          </button>
          <button
            onClick={() => deleteUser(row.id)}
            className="text-white bg-red-500 p-1 rounded-md hover:bg-red-600"
            title={t("delete")}
          >
            <MdDelete />
          </button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[60vh]">
        <ProgressBarWidget />
      </div>
    );
  }

  return (
    <>
      <TableWidget
        isMobile={isMobile}
        px={isMobile ? 16 : 18}
        title={t("users_list")}
        columns={columns}
        rows={users}
        noDataMessage={t("no_users_available")}
        vh={80}
        scrollY={isMobile ? "60vh" : "70vh"}
        minTableWidth={isMobile ? 900 : 1100}
        isAddClick
        onAddClick={() => {
          setSelectedUser(null);
          setOpenModal(true);
        }}
        onRowClick={(row) => setSelectedUser(row)}
        pageSize={isMobile ? 8 : 10}
      />

      {openModal && (
        <UserModal
          openModal={openModal}
          setOpenModal={setOpenModal}
          user={selectedUser}
          Submit={async (formData) => {
            try {
              if (selectedUser?.id) {
                await axiosInstance.put(`/auth/update/${selectedUser.id}`, formData);
                toast.success(t("user_updated"));
              } else {
                await axiosInstance.post("/auth/create", formData);
                toast.success(t("user_created"));
              }
              setOpenModal(false);
              fetchUsers();
            } catch (error) {
              console.error("Error saving user:", error);
              toast.error(t("error_saving_user"));
            }
          }}
        />
      )}

      {openChangePasswordModal && selectedUser && (
        <ChangePasswordModal
          openModal={openChangePasswordModal}
          setOpenModal={setOpenChangePasswordModal}
          user={selectedUser}
        />
      )}
    </>
  );
};

export default Users;