/* eslint-disable react-hooks/exhaustive-deps */
import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { CgClose, CgTrash } from "react-icons/cg";
import { FaEdit } from "react-icons/fa";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";
import DeleteConfirmationModal from "../../widgets/DeleteModal";
import { useParams } from "react-router-dom";
import { toast } from "react-hot-toast";

const MeditationAudios = () => {
  const { id: parentId } = useParams(); // parent meditation/category id
  const { t } = useTranslation();

  const [rows, setRows] = useState([]); // normalized rows
  const [isLoading, setIsLoading] = useState(true);

  // add/edit modal
  const [openModal, setOpenModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formState, setFormState] = useState({
    id: null,            // sub meditation id (for edit)
    title: "",
    description: "",     // optional (kept for parity with backend)
    audio_url: null,     // File or string
  });

  // delete modal
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);

  /* ------------------ Fetch & normalize ------------------ */
  const fetchAudios = async () => {
    try {
      const res = await axiosInstance.get(`/meditation/sub/${parentId}`);
      const list = Array.isArray(res.data?.data) ? res.data.data : [];
      // normalize to { id, title, audio_url, description? }
      const normalized = list.map((it) => ({
        id: it.sub_id ?? it.id,         // prefer sub_id if present
        title: it.title ?? "",
        audio_url: it.audio_url ?? "",
        description: it.description ?? "",
      }));
      setRows(normalized);
    } catch (err) {
      console.error("Error fetching meditation audios:", err?.message || err);
      setRows([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(fetchAudios, 1000);
    return () => clearTimeout(timer);
  }, [parentId]);

  /* ------------------ Modal & form handlers ------------------ */
  const handleOpenModal = (row = null) => {
    if (row) {
      // edit
      setFormState({
        id: row.id,
        title: row.title || "",
        description: row.description || "",
        audio_url: row.audio_url || null, // keep string path; only upload if File picked
      });
    } else {
      // create
      setFormState({
        id: null,
        title: "",
        description: "",
        audio_url: null,
      });
    }
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setFormState({ id: null, title: "", description: "", audio_url: null });
  };

  const handleInputChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "audio_url") {
      setFormState((s) => ({ ...s, audio_url: files?.[0] || null })); // File
    } else {
      setFormState((s) => ({ ...s, [name]: value }));
    }
  };

  /* ------------------ Create/Update ------------------ */
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formState.title.trim()) {
      toast.error(t("Please_enter_a_title"));
      return;
    }

    setIsSubmitting(true);

    try {
      const fd = new FormData();
      fd.append("title", formState.title);
      fd.append("description", formState.description || "");
      fd.append("type", "sub_category");
      fd.append("meditation_id", parentId);

      // only append a new file if user picked one (File)
      if (formState.audio_url instanceof File) {
        fd.append("audio_url", formState.audio_url);
      }

      const isEdit = !!formState.id;
      const url = isEdit
        ? `/meditation/update-sub/${formState.id}`
        : `/meditation/sub/${parentId}`;
      const method = isEdit ? "put" : "post";

      const res = await axiosInstance[method](url, fd, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (res.status === 200 || res.status === 201) {
        toast.success(isEdit ? t("updated_successfully") : t("created_successfully"));
        await fetchAudios();
        handleCloseModal();
      }
    } catch (err) {
      console.error("Error saving meditation audio:", err?.response?.data || err?.message || err);
      toast.error(t("save_failed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  /* ------------------ Delete ------------------ */
  const askDelete = (row) => {
    setSelectedRow(row);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!selectedRow?.id) return;
    try {
      await axiosInstance.delete(`/meditation/delete-sub/${selectedRow.id}`);
      toast.success(t("delete_audio_successfully"));
      setIsDeleteModalOpen(false);
      setSelectedRow(null);
      fetchAudios();
    } catch (err) {
      console.error("Delete failed:", err?.response?.data || err?.message || err);
      toast.error(t("delete_failed"));
    }
  };

  /* ------------------ Table columns ------------------ */
  const columns = [
    {
      key: "index",
      label: t("id"),
      align: "center",
      minWidth: 70,
      render: (_value, _row, index) => index + 1,
    },
    {
      key: "title",
      label: t("title"),
      align: "center",
      minWidth: 240,
      render: (value) => (
        <div
          className="max-w-[320px] mx-auto text-center whitespace-nowrap overflow-hidden text-ellipsis"
          title={value || ""}
        >
          {value || "—"}
        </div>
      ),
    },
    {
      key: "audio_url",
      label: t("audio"),
      align: "center",
      minWidth: 360,
      render: (value) => (
        <div className="w-[400px] max-w-full mx-auto">
          {value ? (
            <audio controls className="w-full h-10">
              <source src={baseURL + value} />
              Your browser does not support the audio element.
            </audio>
          ) : (
            <span className="text-gray-400">{t("no_audio")}</span>
          )}
        </div>
      ),
    },
    {
      key: "actions",
      label: t("actions"),
      align: "center",
      minWidth: 160,
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            className="text-white bg-teal-700 p-2 rounded-lg hover:bg-teal-600"
            onClick={() => handleOpenModal(row)}
            title={t("edit")}
          >
            <FaEdit size={18} />
          </button>
          <button
            className="text-white bg-red-700 p-2 rounded-lg hover:bg-red-600"
            onClick={() => askDelete(row)}
            title={t("delete")}
          >
            <CgTrash size={18} />
          </button>
        </div>
      ),
    },
  ];

  /* ------------------ Loading ------------------ */
  if (isLoading) return <ProgressBarWidget />;

  /* ------------------ Render ------------------ */
  return (
    <>
      <TableWidget
        title={t("audio_meditation")}
        columns={columns}
        rows={rows}
        noDataMessage={t("no_data_available")}
        onAddClick={() => handleOpenModal(null)}
        vh={85}
        scrollX
        minTableWidth={900}
        clampLines={1}
        ellipsisTooltip
      />

      {/* Create/Edit Modal */}
      {openModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50">
          <div className="bg-white rounded-lg shadow-md w-[420px] p-6">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-lg font-semibold">
                {formState.id ? t("edit_audio") : t("create_audio")}
              </h2>
              <button
                className="bg-red-700 text-white p-1 rounded-full hover:bg-red-600"
                onClick={handleCloseModal}
                type="button"
              >
                <CgClose size={20} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">{t("title")}</label>
                <input
                  type="text"
                  name="title"
                  value={formState.title}
                  onChange={handleInputChange}
                  className="mt-1 p-2 border border-gray-300 rounded-md w-full"
                  placeholder={t("enter_title")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">{t("audio")}</label>
                {/* On edit: show small note & don't require file */}
                {formState.id && typeof formState.audio_url === "string" && (
                  <div className="mb-2 text-xs text-gray-500">
                    {t("leave_empty_to_keep_current_audio")}
                  </div>
                )}
                <input
                  type="file"
                  name="audio_url"
                  accept="audio/*"
                  onChange={handleInputChange}
                  className="mt-1 p-2 border border-gray-300 rounded-md w-full"
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`bg-teal-700 text-white p-2 rounded-lg hover:bg-teal-600 w-full transition ${
                    isSubmitting ? "opacity-60 cursor-not-allowed" : ""
                  }`}
                >
                  {isSubmitting ? t("loading") : t("save")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title={t("confirm_deletion")}
        subTitle={t("are_you_sure_you_want_to_delete_this_meditation")}
        cancelBtn={t("cancel")}
        confirmBtn={t("delete")}
      />
    </>
  );
};

export default MeditationAudios;