/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-constant-binary-expression */
import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import axiosInstance from "../../utils/instance_axios";
import { FaEdit, FaTrash } from "react-icons/fa";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

const TreatmentVideo = () => {
    const { id } = useParams();                 // parent video id
    const navigate = useNavigate();
    const { t } = useTranslation();

    const [treatmentVideo, setTreatmentVideo] = useState([]);
    const [loading, setLoading] = useState(true);

    const [openModal, setOpenModal] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [isUploaded, setIsUploaded] = useState(false);

    const [formData, setFormData] = useState({
        id: null,
        title: "",
        description: "",
        category: "sub_category",
        video_id: null,
    });

    // --- Fetch ---
    const fetchTreatmentVideo = async () => {
        try {
            const res = await axiosInstance.get(`/videos/treatment-video/${id}`);
            setTreatmentVideo(res?.data?.data || []);
        } catch (error) {
            console.error("fetch error:", error?.response?.data || error.message);
            toast.error(error?.response?.data?.error || t("fetch_failed"));
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTreatmentVideo();
    }, [fetchTreatmentVideo]);

    console.log("treatmentVideo: ", treatmentVideo);

    // --- Modal handlers ---
    const handleOpenModal = () => {
        setIsEditing(false);
        setFormData({
            id: null,
            title: "",
            description: "",
            category: "sub_category",
            video_id: Number(id) || null,
        });
        setOpenModal(true);
    };

    const handleCloseModal = () => {
        setOpenModal(false);
        setIsEditing(false);
        setIsUploaded(false);
        setFormData({
            id: null,
            title: "",
            description: "",
            category: "sub_category",
            video_id: Number(id) || null,
        });
    };

    const handleEdit = (row) => {
        setIsEditing(true);
        setFormData({
            id: row.id,
            title: row.text || "",
            description: row.description || "",
            category: row.type || "sub_category",
            video_id: row.video_id ?? Number(id) ?? null,
        });
        setOpenModal(true);
    };

    // --- Form handling ---
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData((p) => ({ ...p, [name]: value }));
    };

    // --- Delete ---
    const handleDelete = async (videoId) => {
        try {
            const res = await axiosInstance.delete(`/videos/treatment-video/${videoId}`);
            toast.success(res?.data?.message || t("deleted_successfully"));
            fetchTreatmentVideo();
        } catch (error) {
            console.error("delete error:", error?.response?.data || error.message);
            toast.error(error?.response?.data?.error || t("delete_failed"));
        }
    };

    // --- Submit (create/update) ---
    const handleSubmit = async (e) => {
        e.preventDefault();

        const { title, description, category } = formData;
        if (!title?.trim()) {
            toast.error(t("title_required"));
            return;
        }

        setIsUploaded(true);

        const payload = {
            text: title.trim(),
            description: description?.trim() || "",
            type: category || "sub_category",
        };

        try {
            if (isEditing) {
                await axiosInstance.put(`/videos/update-sub/${formData.id}`, payload, {
                    headers: { "Content-Type": "application/json" },
                });
            } else {
                await axiosInstance.post(`/videos/treatment-video/${id}`, payload, {
                    headers: { "Content-Type": "application/json" },
                });
            }
            toast.success(isEditing ? t("updated_successfully") : t("created_successfully"));
            fetchTreatmentVideo();
            handleCloseModal();
        } catch (error) {
            console.error("submit error:", error?.response?.data || error.message);
            toast.error(error?.response?.data?.error || t("save_failed"));
        } finally {
            setIsUploaded(false);
        }
    };

    if (loading) return <ProgressBarWidget />;

    // ---------- Columns (use render(value, row, index)) ----------
    const columns = [
        {
            key: "id",
            label: t("id"),
            align: "center",
            minWidth: 80,
            render: (_value, _row, index) => <p className="font-bold text-lg">{index + 1}</p>,
        },
        {
            key: "text", // the actual field coming from API
            label: t("title"),
            align: "center",
            minWidth: 260,
            render: (value, row) => (
                <div className="w-full h-full flex items-center justify-center hover:underline hover:text-[#51B4E2] cursor-pointer line-clamp-2" onClick={() => navigate(`/dashboard/online-treatment/treatment-video/treatment-items/${row.id}`)}>
                    <p
                        className="font-semibold "
                        title={value}
                        onClick={() =>
                            navigate(`/dashboard/online-treatment/treatment-video/treatment-items/${row.id}`)
                        }
                    >
                        {value}
                    </p>
                </div>
            ),
        },
        {
            key: "actions",
            label: t("actions"),
            align: "center",
            minWidth: 180,
            render: (_value, row) => (
                <div className="flex items-center justify-center gap-2">
                    <button
                        className="bg-teal-600 text-white rounded-md p-2 hover:bg-teal-500"
                        onClick={() => handleEdit(row)}
                        title={t("edit")}
                    >
                        <FaEdit size={18} />
                    </button>
                    <button
                        className="bg-red-600 text-white rounded-md p-2 hover:bg-red-500"
                        onClick={() => handleDelete(row.id)}
                        title={t("delete")}
                    >
                        <FaTrash size={18} />
                    </button>
                </div>
            ),
        },
    ];

    return (
        <>
            <TableWidget
                title={t("treatment_videos")}
                columns={columns}
                rows={treatmentVideo}
                noDataMessage="no_data_available"
                vh={80}
                isAddClick
                onAddClick={handleOpenModal}
            // optional, if your TableWidget supports:
            // scrollX
            // minTableWidth={800}
            // scrollY="65vh"
            />

            {openModal && (
                <div className="fixed inset-0 flex items-center justify-center bg-gray-900/50 z-50">
                    <div className="bg-white p-6 rounded shadow-lg w-full max-w-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-semibold">
                                {isEditing ? t("edit_video") : t("add_video")}
                            </h2>
                            <button
                                onClick={handleCloseModal}
                                className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                                aria-label={t("close")}
                            >
                                &times;
                            </button>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            {/* Title */}
                            <div>
                                <label htmlFor="videoTitle" className="block text-sm font-medium mb-1">
                                    {t("video_title")}
                                </label>
                                <input
                                    id="videoTitle"
                                    name="title"
                                    type="text"
                                    value={formData.title}
                                    onChange={handleInputChange}
                                    placeholder={t("enter_video_title")}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                                    required
                                />
                            </div>

                            {/* Category (fixed to sub-category) */}
                            <div>
                                <label className="block text-sm font-medium mb-1">{t("category")}</label>
                                <input
                                    type="text"
                                    value={t("sub_category")}
                                    readOnly
                                    className="w-full px-3 py-2 bg-gray-100 text-gray-600 border border-gray-300 rounded-md"
                                />
                            </div>

                            {/* Submit */}
                            <div className="flex justify-end">
                                <button
                                    type="submit"
                                    disabled={isUploaded}
                                    className={`px-4 py-2 text-white rounded-md ${isUploaded ? "bg-teal-400 cursor-not-allowed" : "bg-teal-600 hover:bg-teal-500"
                                        }`}
                                >
                                    {isUploaded ? t("loading") : isEditing ? t("update") : t("submit")}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default TreatmentVideo;