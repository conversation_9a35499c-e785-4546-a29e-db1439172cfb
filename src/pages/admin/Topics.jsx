import TableWidget from "../../widgets/TableWidget";
import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import axiosInstance, { baseURL } from '../../utils/instance_axios';
import { MdClose, MdDelete } from "react-icons/md";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";
import DeleteConfirmationModal from "../../widgets/DeleteModal";
import { FaEdit } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

const Topics = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [topics, setTopics] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentTopic, setCurrentTopic] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    type: 'category',
    parent_id: ''
  });

  useEffect(() => {
    fetchTopics();
  }, []);

  const fetchTopics = async () => {
    try {
      const response = await axiosInstance.get('/topics');
      setTopics(Array.isArray(response.data?.data) ? response.data.data : []);
    } catch (error) {
      console.error("Error fetching topics:", error);
    } finally {
      setLoading(false);
    }
  };

  const openModal = (topic = null) => {
    setCurrentTopic(topic);
    setImagePreview(topic?.image ? `${baseURL}${topic.image}` : null);
    setFormData({
      name: topic?.name || '',
      type: topic?.type || 'category',
      parent_id: topic?.parent_id || ''
    });
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setCurrentTopic(null);
    setImageFile(null);
    setImagePreview(null);
    setFormData({ name: '', type: 'category', parent_id: '' });
    setIsModalOpen(false);
  };

  const handleImageChange = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImageFile(file);
    setImagePreview(URL.createObjectURL(file));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value
    }));
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    const submitData = new FormData();
    submitData.append("name", formData.name);
    submitData.append("type", formData.type);
    if (formData.parent_id) submitData.append("parent_id", formData.parent_id);
    if (imageFile) submitData.append("image", imageFile);

    try {
      if (currentTopic) {
        await axiosInstance.put(`/topics/${currentTopic.id}`, submitData);
      } else {
        await axiosInstance.post("/topics", submitData);
      }
      closeModal();
      fetchTopics();
    } catch (error) {
      console.error("Error saving topic:", error);
    }
  };

  const handleToggleTopicStatus = async (id, isActive) => {
    try {
      await axiosInstance.put(`/topics/${id}/toggle`, { is_active: !isActive });
      fetchTopics();
    } catch (error) {
      console.error("Error toggling topic status:", error);
    }
  };

  const editItem = (item) => {
    openModal(item);
  };

  const askDeleteItem = (item) => {
    setSelectedTopic(item);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteTopic = async () => {
    if (!selectedTopic?.id) return;
    try {
      await axiosInstance.delete(`/topics/${selectedTopic.id}`);
      fetchTopics();
      setIsDeleteModalOpen(false);
      setSelectedTopic(null);
    } catch (error) {
      console.error("Error deleting topic:", error);
    }
  };

  // IMPORTANT: TableWidget passes (value, row) to render()
  const columns = [
    {
      key: 'id',
      label: t('id'),
      align: 'center',
      // default renderer shows row.id, so no custom render needed
    },
    {
      key: 'image',
      label: t('image'),
      align: 'center',
      render: (value, row) =>
        row?.image ? (
          <div className="w-full h-full flex items-center justify-center">
            <img
              src={`${baseURL}${row.image}`}
              alt={row.name}
              className="w-20 h-20 object-cover rounded"
            />
          </div>
        ) : (
          <span className="text-gray-400">—</span>
        ),
    },
    {
      key: 'name',
      label: t('name'),
      align: 'center',
      render: (value, row) => (
        <div className="w-full h-full flex items-center justify-center hover:underline cursor-pointer" onClick={() => navigate(`/dashboard/topics/${row.id}/${row.name}/notification`)}>
          <span className="text-gray-800 font-semibold ">{row.name}</span>
        </div>
      ),
    },
    {
      key: 'is_active',
      label: t('status'),
      align: 'center',
      render: (value, row) => (
        <div className="flex items-center justify-center">
          <div
            onClick={() => handleToggleTopicStatus(row.id, row.is_active)}
            className={`relative w-14 h-8 rounded-full p-1 cursor-pointer transition-colors duration-300 ${row.is_active ? 'bg-teal-600' : 'bg-red-500'
              }`}
            role="switch"
            aria-checked={!!row.is_active}
            aria-label={t('status')}
            title={row.is_active ? t('active') : t('inactive')}
          >
            <div
              className={`absolute top-1 left-1 bg-white w-6 h-6 rounded-full shadow-md transform transition-transform duration-300 ${row.is_active ? 'translate-x-6' : ''
                }`}
            />
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      label: t('actions'),
      align: 'center',
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => editItem(row)}
            className="text-white bg-teal-600 hover:bg-teal-500 p-1 rounded-md"
            title={t('edit')}
          >
            <FaEdit size={20} />
          </button>
          <button
            onClick={() => askDeleteItem(row)}
            className="text-white bg-red-600 hover:bg-red-700 p-1 rounded-md"
            title={t('delete')}
          >
            <MdDelete size={20} />
          </button>
        </div>
      ),
    },
  ];

  if (loading) return <ProgressBarWidget />;

  const isMobile = window.innerWidth < 768;

  return (
    <div>
      <TableWidget
        px={14}
        isMobile={isMobile}
        title={t("topics")}
        columns={columns}
        rows={topics}
        isAddClick
        onAddClick={() => setIsModalOpen(true)}
        noDataMessage={t("no_data_available")}
        vh={90}
      />

      {/* Create / Edit Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
          <div className="bg-white rounded-lg p-6 w-[500px]">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">{currentTopic ? t('edit_topic') : t('add_topic')}</h2>
              <button className="text-white bg-red-600 rounded-full p-1" onClick={closeModal}>
                <MdClose size={20} />
              </button>
            </div>
            <form onSubmit={handleFormSubmit}>
              <label htmlFor="image" className="block mb-2">
                <span className="text-gray-700">{t('upload_image')}</span>
                <input type="file" accept="image/*" id="image" onChange={handleImageChange} className="block w-full" />
              </label>

              {imagePreview && (
                <img src={imagePreview} alt="Preview" className="w-24 h-24 mt-2 mb-4 object-cover rounded" />
              )}

              <label className="block mb-2">
                <span className="text-gray-700">{t('topic')}</span>
                <input
                  name="name"
                  type="text"
                  className="w-full p-2 border rounded mt-1"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </label>

              <label className="block mb-2">
                <span className="text-gray-700">{t('type')}</span>
                <select
                  name="type"
                  className="w-full p-2 border rounded mt-1"
                  value={formData.type}
                  onChange={handleInputChange}
                >
                  <option value="category">{t('category')}</option>
                  <option value="sub_category">{t('sub_category')}</option>
                </select>
              </label>

              {formData.type === 'sub_category' && (
                <label className="block mb-2">
                  <span className="text-gray-700">{t('parent_topic_id')}</span>
                  <input
                    name="parent_id"
                    type="number"
                    className="w-full p-2 border rounded mt-1"
                    value={formData.parent_id}
                    onChange={handleInputChange}
                    required
                  />
                </label>
              )}

              <div className="flex justify-end mt-4">
                <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded">
                  {currentTopic ? t('update_topic') : t('add_topic')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteTopic}
        title={t('confirm_deletion')}
        subTitle={t('are_you_sure_you_want_to_delete_this_topic')}
        cancelBtn={t('cancel')}
        confirmBtn={t('delete')}
      />
    </div>
  );
};

export default Topics;