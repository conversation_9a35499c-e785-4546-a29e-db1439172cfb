import { useState, useEffect } from 'react';
import axiosInstance from '../../utils/instance_axios';
import { useNavigate } from 'react-router-dom';

const RegisterAdmin = () => {
  const [formData, setFormData] = useState({
    clinic_name: '',
    address_clinic: '',
    user_id: '', // Set when an email is selected
    phone_clinic: '',
    link_whatsapp: '',
    link_telegram: '',
    link_instagram: '',
    link_youtube: '',
    link_facebook: '',
    link_twitter: '',
    link_tiktok: '',
  });
  const [emails, setEmails] = useState([]); // State to store fetched emails
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();

  // Fetch emails and IDs when the component mounts
  useEffect(() => {
    const fetchEmails = async () => {
      try {
        const emailList = await getAllEmails();
        setEmails(emailList); // Set the emails state with fetched email list
      } catch (error) {
        console.error('Error fetching emails:', error);
        setError('Failed to fetch emails');
      }
    };
    fetchEmails();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'email') {
      // Find user_id corresponding to the selected email
      const selectedUser = emails.find((emailObj) => emailObj.email === value);
      setFormData({
        ...formData,
        email: value,
        user_id: selectedUser ? selectedUser.user_id : '',
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    console.log("Submitting admin registration with user ID:", formData.user_id); // Debugging: log user_id

    try {
      const response = await axiosInstance.post('/clinics/register', formData);
      if (response.status === 201) {
        console.log("Admin registered successfully!");
        setSuccess('Admin registered successfully!');
        setFormData({
          clinic_name: '',
          address_clinic: '',
          user_id: '',
          phone_clinic: '',
          link_whatsapp: '',
          link_telegram: '',
          link_instagram: '',
          link_youtube: '',
          link_facebook: '',
          link_twitter: '',
          link_tiktok: '',
        });
        navigate('/login');
      }
    } catch (error) {
      setError('Error registering admin');
      console.error("Error registering admin:", error);
    }
  };

  // Get all emails from users table by admin role
  const getAllEmails = async () => {
    try {
      const response = await axiosInstance.get('/auth');
      return response.data.data.map((user) => ({
        email: user.email,
        user_id: user.id,
      }));
    } catch (error) {
      console.error('Error fetching emails:', error);
      throw error;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white shadow-md rounded-lg p-8 max-w-4xl w-full">
        <h2 className="text-2xl font-bold mb-6 text-center">Register Admin</h2>
        {error && <div className="text-red-500 mb-4">{error}</div>}
        {success && <div className="text-green-500 mb-4">{success}</div>}
        
        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Clinic Name */}
          <div>
            <label className="block text-sm font-medium">Clinic Name</label>
            <input
              type="text"
              name="clinic_name"
              value={formData.clinic_name}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>

          {/* Email Dropdown */}
          <div>
            <label className="block text-sm font-medium">Email</label>
            <select
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            >
              <option value="">Select an email</option>
              {emails.map((emailObj, index) => (
                <option key={index} value={emailObj.email}>
                  {emailObj.email}
                </option>
              ))}
            </select>
          </div>

          {/* Phone */}
          <div>
            <label className="block text-sm font-medium">Phone</label>
            <input
              type="text"
              name="phone_clinic"
              value={formData.phone_clinic}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm font-medium">Address</label>
            <input
              type="text"
              name="address_clinic"
              value={formData.address_clinic}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>

          {/* Social Media Links */}
          {['link_whatsapp', 'link_telegram', 'link_instagram', 'link_youtube', 'link_facebook', 'link_twitter', 'link_tiktok'].map((link, idx) => (
            <div key={idx}>
              <label className="block text-sm font-medium">{link.replace('link_', '').replace(/_/g, ' ')} Link</label>
              <input
                type="text"
                name={link}
                value={formData[link]}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
          ))}

          {/* Submit Button */}
          <div className="md:col-span-2">
            <button type="submit" className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 transition-colors">
              Register Admin
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RegisterAdmin;
