/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState, useRef } from "react";
import axiosInstance, { baseURL } from "../../utils/instance_axios";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { FaEdit, FaEye } from "react-icons/fa";
import { CgClose } from "react-icons/cg";
import { useNavigate } from "react-router-dom";
import TableWidget from "../../widgets/TableWidget";
import FallbackImage from "../../assets/arabSBT_icon.png";
import ProgressBarWidget from "../../widgets/ProgresBarWidget";

const Books = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);

  // Add modal
  const [modalOpen, setModalOpen] = useState(false);
  const [newBook, setNewBook] = useState({
    name: "",
    price: "",
    coverImage: null,        // local file
    secondaryImage: null,    // local file
  });
  const [coverPreview, setCoverPreview] = useState(null);
  const [secondaryPreview, setSecondaryPreview] = useState(null);

  // Edit modal
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editBook, setEditBook] = useState(null); // row + any local files

  const coverInputRef = useRef(null);
  const secondaryInputRef = useRef(null);

  const navigate = useNavigate();
  const { t } = useTranslation();

  const fetchBooks = async () => {
    setLoading(true);
    try {
      const res = await axiosInstance.get("/books");
      const list = res?.data?.data?.data ?? [];
      const mapped = list.map((b) => ({
        id: b.id,
        name: b.name ?? "",
        imageUrl: b.image ? `${baseURL}${b.image}` : FallbackImage,
        secondaryImageUrl: b.image_secondary ? `${baseURL}${b.image_secondary}` : FallbackImage,
        totalPages: Number(b.totalPages || 0),
        averageRating: Number(b.averageRating || 0),
        price: b.original_price ?? 0,
        is_exists: !!b.is_exists,
        created_at: b.created_at,
      }));
      setBooks(mapped);
    } catch (e) {
      console.error("Error fetching books:", e);
      toast.error(t("failed_to_load_books") || "Failed to load books");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBooks();
  }, []);

  // Revoke object URLs when previews change/unmount
  useEffect(() => {
    return () => {
      if (coverPreview) URL.revokeObjectURL(coverPreview);
      if (secondaryPreview) URL.revokeObjectURL(secondaryPreview);
    };
  }, [coverPreview, secondaryPreview]);

  // Toggle book status
  const handleToggle = async (bookId, currentStatus) => {
    try {
      const res = await axiosInstance.put(`/books/${bookId}/status`, {
        is_exists: !currentStatus,
      });
      if (res.status === 200) {
        setBooks((prev) =>
          prev.map((bk) => (bk.id === bookId ? { ...bk, is_exists: !currentStatus } : bk))
        );
        toast.success(t("book_status_updated") || "Book status updated");
      }
    } catch (e) {
      console.error("Error updating book status:", e);
      toast.error(t("failed_to_update_book_status") || "Failed to update book status");
    }
  };

  // Create book (uses API field names: image, image_secondary)
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const fd = new FormData();
      fd.append("name", newBook.name);
      fd.append("price", newBook.price);
      if (newBook.coverImage) fd.append("image", newBook.coverImage); // ✅ backend expects "image"
      if (newBook.secondaryImage) fd.append("image_secondary", newBook.secondaryImage); // ✅ backend expects "image_secondary"

      const res = await axiosInstance.post("/books", fd, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (res.status === 201) {
        setModalOpen(false);
        setNewBook({ name: "", price: "", coverImage: null, secondaryImage: null });
        setCoverPreview(null);
        setSecondaryPreview(null);
        toast.success(t("book_added_successfully") || "Book added successfully!");
        fetchBooks();
      }
    } catch (e) {
      console.error("Error adding book:", e);
      toast.error(t("failed_to_add_book") || "Failed to add book");
    }
  };

  // Update book (uses same API field names)
  const handleEditSubmit = async (e) => {
    e.preventDefault();
    if (!editBook?.id) return;

    try {
      const fd = new FormData();
      fd.append("name", editBook.name ?? "");
      fd.append("price", editBook.price ?? "");
      if (editBook.coverImage instanceof File) fd.append("image", editBook.coverImage); // ✅
      if (editBook.secondaryImage instanceof File) fd.append("image_secondary", editBook.secondaryImage); // ✅

      const res = await axiosInstance.put(`/books/${editBook.id}`, fd, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (res.status === 200) {
        setEditModalOpen(false);
        setEditBook(null);
        setCoverPreview(null);
        setSecondaryPreview(null);
        toast.success(t("book_updated_successfully") || "Book updated successfully!");
        fetchBooks();
      }
    } catch (e) {
      console.error("Error updating book:", e);
      toast.error(t("failed_to_update_book") || "Failed to update book");
    }
  };

  if (loading) return <ProgressBarWidget />;

  // Columns for TableWidget (render(value, row, idx))
  const columns = [
    {
      label: t("cover_image", "Cover image"),
      key: "imageUrl",
      align: "center",
      render: (value, _row) => (
        <div className="flex justify-center">
          <img src={value || FallbackImage} alt={t("cover_image", "Cover image")} className="w-16 h-20 object-cover rounded shadow" />
        </div>
      ),
    },
    {
      label: t("secondary_image", "Secondary image"),
      key: "secondaryImageUrl",
      align: "center",
      render: (value, _row) => (
        <div className="flex justify-center">
          <img src={value || FallbackImage} alt={t("secondary_image", "Secondary image")} className="w-16 h-20 object-cover rounded shadow" />
        </div>
      ),
    },
    {
      label: t("name", "Name"),
      key: "name",
      align: "right",
      render: (value, _row) => <div className="text-right cursor-pointer hover:underline" onClick={() => navigate(`/dashboard/add-pages/${_row.id}/${_row.name}`)}>{value}</div>,
    },
    {
      label: t("pages", "Pages"),
      key: "totalPages",
      align: "center",
      render: (value, _row) => <div className="text-center">{Number(value) || 0}</div>,
    },
    {
      label: t("rating", "Rating"),
      key: "averageRating",
      align: "center",
      render: (value, _row) => (
        <div className="text-center">{Number.isFinite(Number(value)) ? Number(value).toFixed(2) : "0.00"}</div>
      ),
    },
    {
      label: t("price", "Price"),
      key: "price",
      align: "center",
      render: (value, _row) => <div className="text-center">{value}</div>,
    },
    {
      label: t("status", "Status"),
      key: "is_exists",
      align: "center",
      render: (value, row) => (
        <button
          type="button"
          onClick={() => handleToggle(row.id, !!value)}
          className={`relative w-12 h-6 rounded-full p-1 transition-colors duration-300 ${value ? "bg-green-500" : "bg-red-500"}`}
          aria-label={value ? t("active", "Active") : t("inactive", "Inactive")}
          title={value ? t("active", "Active") : t("inactive", "Inactive")}
        >
          <span className={`absolute top-1 left-1 bg-white w-4 h-4 rounded-full shadow-md transform transition-transform duration-300 ${value ? "translate-x-6" : ""}`} />
        </button>
      ),
    },
    {
      label: t("date_added", "Date added"),
      key: "created_at",
      align: "center",
      render: (value, _row) => (
        <div className="text-center">{value ? new Date(value).toLocaleDateString("en-GB") : "-"}</div>
      ),
    },
    {
      label: t("actions", "Actions"),
      key: "actions",
      align: "center",
      render: (_value, row) => (
        <div className="flex items-center justify-center gap-2">
          <button
            type="button"
            className="text-green-700 text-lg p-2 rounded hover:text-green-600"
            onClick={() => {
              setEditBook(row);
              setCoverPreview(null);
              setSecondaryPreview(null);
              setEditModalOpen(true);
            }}
            title={t("edit_book", "Edit book")}
          >
            <FaEdit />
          </button>
          <button
            type="button"
            className="text-blue-700 text-lg p-2 rounded hover:text-blue-600"
            onClick={() => navigate(`/dashboard/preview/${row.id}`)}
            title={t("preview", "Preview")}
          >
            <FaEye />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <TableWidget
        title={t("Books")}
        columns={columns}
        rows={books}
        noDataMessage={t("no_data_available")}
        onAddClick={() => {
          setModalOpen(true);
          setNewBook({ name: "", price: "", coverImage: null, secondaryImage: null });
          setCoverPreview(null);
          setSecondaryPreview(null);
        }}
        vh={90}
      />

      {/* Add Book Modal */}
      {modalOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded shadow-lg w-[600px] max-w-[95vw] relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
              onClick={() => setModalOpen(false)}
              aria-label={t("close")}
            >
              <CgClose size={20} />
            </button>

            <form className="flex flex-col pt-4 gap-4" onSubmit={handleSubmit}>
              <h2 className="text-2xl font-bold">{t("add_book")}</h2>

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_name")}</span>
                <input
                  type="text"
                  className="w-full p-2 border border-gray-300 rounded"
                  value={newBook.name}
                  onChange={(e) => setNewBook((p) => ({ ...p, name: e.target.value }))}
                  required
                />
              </label>

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_price")}</span>
                <input
                  type="number"
                  className="w-full p-2 border border-gray-300 rounded"
                  value={newBook.price}
                  onChange={(e) => setNewBook((p) => ({ ...p, price: e.target.value }))}
                  required
                />
              </label>

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_cover_1")}</span>
                <input
                  type="file"
                  accept="image/*"
                  className="w-full p-2 border border-gray-300 rounded"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    setNewBook((p) => ({ ...p, coverImage: file }));
                    setCoverPreview(file ? URL.createObjectURL(file) : null);
                  }}
                  required
                />
              </label>

              {coverPreview && (
                <div>
                  <span className="block text-gray-700 font-bold mb-2">{t("cover_preview")}</span>
                  <img src={coverPreview} alt="Preview" className="w-full h-auto rounded shadow-md" />
                </div>
              )}

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_cover_2")}</span>
                <input
                  type="file"
                  accept="image/*"
                  className="w-full p-2 border border-gray-300 rounded"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    setNewBook((p) => ({ ...p, secondaryImage: file }));
                    setSecondaryPreview(file ? URL.createObjectURL(file) : null);
                  }}
                />
              </label>

              {secondaryPreview && (
                <div>
                  <span className="block text-gray-700 font-bold mb-2">{t("cover_preview")}</span>
                  <img src={secondaryPreview} alt="Preview" className="w-full h-auto rounded shadow-md" />
                </div>
              )}

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="bg-teal-700 text-white p-2 rounded-lg hover:bg-teal-600 transition"
                >
                  {t("create_book")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Book Modal */}
      {editModalOpen && editBook && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded shadow-lg w-[600px] max-w-[95vw] relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
              onClick={() => setEditModalOpen(false)}
              aria-label={t("close")}
            >
              <CgClose size={20} />
            </button>

            <h2 className="text-2xl font-bold mb-4">{t("edit_book")}</h2>

            <form onSubmit={handleEditSubmit} className="flex flex-col gap-4">
              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_name")}</span>
                <input
                  type="text"
                  className="w-full p-2 border border-gray-300 rounded"
                  value={editBook.name ?? ""}
                  onChange={(e) => setEditBook((p) => ({ ...p, name: e.target.value }))}
                  required
                />
              </label>

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_price")}</span>
                <input
                  type="number"
                  className="w-full p-2 border border-gray-300 rounded"
                  value={editBook.price ?? ""}
                  onChange={(e) => setEditBook((p) => ({ ...p, price: e.target.value }))}
                  required
                />
              </label>

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("book_cover")}</span>
                <input
                  type="file"
                  accept="image/*"
                  className="w-full p-2 border border-gray-300 rounded"
                  ref={coverInputRef}
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    setEditBook((p) => ({ ...p, coverImage: file }));
                    setCoverPreview(file ? URL.createObjectURL(file) : null);
                  }}
                />
              </label>

              <label className="block">
                <span className="block text-gray-700 font-bold mb-2">{t("image_secondary")}</span>
                <input
                  type="file"
                  accept="image/*"
                  className="w-full p-2 border border-gray-300 rounded"
                  ref={secondaryInputRef}
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    setEditBook((p) => ({ ...p, secondaryImage: file }));
                    setSecondaryPreview(file ? URL.createObjectURL(file) : null);
                  }}
                />
              </label>

              {(coverPreview || secondaryPreview) && (
                <div className="grid grid-cols-2 gap-3">
                  {coverPreview && <img src={coverPreview} alt="Cover preview" className="rounded shadow" />}
                  {secondaryPreview && <img src={secondaryPreview} alt="Secondary preview" className="rounded shadow" />}
                </div>
              )}

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="bg-teal-700 text-white px-4 py-2 rounded-lg hover:bg-teal-600 transition"
                >
                  {t("update")}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default Books;