import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
    MdEmail, MdLock, MdPersonOutline, MdPhone
} from 'react-icons/md';

import Logo from '../assets/arab_cbt_logo.png';
import CustomInput from '../widgets/CustomInput';
import axiosInstance from '../utils/instance_axios';

const LS_KEY = 'arabcbt:signupDraft';

/* helpers ───────────────────────────────────*/
const loadDraft = () => JSON.parse(localStorage.getItem(LS_KEY) || '{}');
const saveDraft = obj => localStorage.setItem(LS_KEY, JSON.stringify(obj));
const clearDraft = () => localStorage.removeItem(LS_KEY);

export default function Register() {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const isRtl = ['ar', 'he'].includes(i18n.language);

    /* which screen are we on? 0-name… 1-age 2-gender */
    const [step, setStep] = useState(0);

    /* ---------- step-1 fields ---------- */
    const [fields, setFields] = useState(() => ({
        name: { value: loadDraft().name ?? '', focused: false },
        username: { value: loadDraft().nickname ?? '', focused: false },
        email: { value: loadDraft().email ?? '', focused: false },
        phone: { value: loadDraft().phone ?? '', focused: false },
        password: { value: '', focused: false },          // never pre-fill passwords
    }));

    /* ---------- step-2 age ---------- */
    const [age, setAge] = useState(loadDraft().age ?? '');

    /* ---------- step-3 gender ---------- */
    const [gender, setGender] = useState(loadDraft().gender ?? '');

    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    /* ───── field helpers ───── */
    const handleFieldChange = (id, value) =>
        setFields(p => ({ ...p, [id]: { value, focused: value !== '' } }));

    /* ────── step navigation ────── */
    const nextStep = () => setStep(s => s + 1);
    const prevStep = () => setStep(s => s - 1);

    /* ────── step-1 “Next” ────── */
    const saveStep1 = () => {
        const missing = Object.entries(fields).find(
            ([, v]) => !v.value.trim()
        );
        if (missing) return setError(t('please_fill_all_fields'));

        saveDraft({
            name: fields.name.value.trim(),
            nickname: fields.username.value.trim(),
            email: fields.email.value.trim(),
            phone: fields.phone.value.trim(),
            password: fields.password.value,
        });
        setError('');
        nextStep();
    };

    /* ────── step-2 “Next” ────── */
    const saveStep2 = () => {
        if (!age.trim()) return setError(t('please_fill_all_fields'));
        saveDraft({ ...loadDraft(), age: age.trim() });
        setError('');
        nextStep();
    };

    /* ────── FINAL submit ────── */
    const handleRegister = async () => {
        if (!gender) return setError(t('please_fill_all_fields'));

        const payload = { ...loadDraft(), gender };
        try {
            setIsLoading(true);
            await axiosInstance.post('/auth/register', payload);

            clearDraft();
            navigate('/login');
        } catch (err) {
            console.error(err);
            setError(err.response?.data?.message || t('something_went_wrong'));
        } finally {
            setIsLoading(false);
        }
    };

    /* ================================================================= */
    return (
        <div
            className="m-0 p-0 h-screen w-full flex items-center justify-center"
            dir={isRtl ? 'rtl' : 'ltr'}
        >
            <div className="w-full max-w-[600px] h-[800px] rounded-3xl shadow-lg flex flex-col overflow-hidden">

                {/* Top Half - Blue */}
                <div className="h-1/2 bg-[#51B4E2] flex flex-col items-center justify-center rounded-t-3xl">
                    <img src={Logo} alt="ArabCBT" className="w-[150px] h-[150px]" />
                    <p className="text-white text-4xl font-bold flex gap-2" dir="ltr">
                        {t('name_app_1')} <span>{t('name_app_2')}</span>
                    </p>
                    <span className="text-sm text-white mt-2">{t('subtitle_login_account')}</span>
                </div>

                {/* Bottom Half - White */}
                <div className="h-1/2 bg-white relative rounded-b-3xl flex items-center justify-center px-4">
                    {/* Form Card Inside */}
                    <div className={`w-full max-w-[500px] p-6 bg-white shadow-lg rounded-3xl absolute ${step === 0 ? '-top-20' : step === 1 ? '-top-20' : '-top-14'}`}>
                        {error && <p className="text-red-500 text-center mb-2">{error}</p>}

                        {/* ───────── STEP 1 ───────── */}
                        {step === 0 && (
                            <>
                                <CustomInput id="name" label="name" Icon={MdPersonOutline}
                                    value={fields.name.value} focused={fields.name.focused}
                                    setFocused={(id, f) => setFields(p => ({ ...p, [id]: { ...p[id], focused: f } }))}
                                    onChange={handleFieldChange} isRTL={isRtl}
                                />
                                <CustomInput id="username" label="nickname" Icon={MdPersonOutline}
                                    value={fields.username.value} focused={fields.username.focused}
                                    setFocused={(id, f) => setFields(p => ({ ...p, [id]: { ...p[id], focused: f } }))}
                                    onChange={handleFieldChange} isRTL={isRtl}
                                />
                                <CustomInput id="email" label="email" type="email" Icon={MdEmail}
                                    value={fields.email.value} focused={fields.email.focused}
                                    setFocused={(id, f) => setFields(p => ({ ...p, [id]: { ...p[id], focused: f } }))}
                                    onChange={handleFieldChange} isRTL={isRtl}
                                />
                                <CustomInput id="phone" label="phone_number" type="tel" Icon={MdPhone}
                                    value={fields.phone.value} focused={fields.phone.focused}
                                    setFocused={(id, f) => setFields(p => ({ ...p, [id]: { ...p[id], focused: f } }))}
                                    onChange={handleFieldChange} isRTL={isRtl}
                                />
                                <CustomInput id="password" label="password" type="password" Icon={MdLock}
                                    value={fields.password.value} focused={fields.password.focused}
                                    setFocused={(id, f) => setFields(p => ({ ...p, [id]: { ...p[id], focused: f } }))}
                                    onChange={handleFieldChange} isRTL={isRtl}
                                />

                                <button
                                    type="button"
                                    onClick={saveStep1}
                                    className="w-full mt-4 py-3 bg-[#51B4E2] text-white font-bold rounded-md hover:bg-[#419cc0]"
                                >
                                    {t('next')}
                                </button>
                            </>
                        )}

                        {/* ───────── STEP 2 ───────── */}
                        {step === 1 && (
                            <>
                                {/* Age selector (scroll list 1-100) */}
                                <label htmlFor="age" className="block mb-1 font-bold text-2xl text-center">
                                    {t('age')}
                                </label>

                                <select
                                    id="age"
                                    value={age}
                                    onChange={e => setAge(e.target.value)}
                                    size={6}                           /* shows 6 rows → scrolls for the rest */
                                    className="
                                        w-full p-3 bg-white
                                        scrollbar-none
                                        overflow-y-auto
                                    "
                                >

                                    {Array.from({ length: 100 }, (_, i) => i + 1).map(n => (
                                        <option key={n} value={n} className='text-center text-4xl'>
                                            {n}
                                        </option>
                                    ))}
                                </select>

                                <div className="flex gap-2 mt-4">
                                    <button
                                        type="button"
                                        onClick={saveStep2}
                                        className="flex-1 py-3 bg-[#51B4E2] text-white rounded-md"
                                    >
                                        {t('next')}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={prevStep}
                                        className="flex-1 py-3 bg-gray-300 rounded-md"
                                    >
                                        {t('previous')}
                                    </button>

                                </div>
                            </>
                        )}

                        {/* ───────── STEP 3 ───────── */}
                        {step === 2 && (
                            <div className="">
                                <label className="block mb-2 font-bold text-center">{t('gender_label')}</label>
                                <div className="flex items-center justify-center gap-4 mb-4">
                                    <label className="flex items-center gap-1">
                                        <input type="radio" name="gender" value="male"
                                            checked={gender === 'male'}
                                            onChange={e => setGender(e.target.value)}
                                        />
                                        {t('male')}
                                    </label>
                                    <label className="flex items-center gap-1">
                                        <input type="radio" name="gender" value="female"
                                            checked={gender === 'female'}
                                            onChange={e => setGender(e.target.value)}
                                        />
                                        {t('female')}
                                    </label>
                                </div>

                                <div className="flex gap-2">
                                    <button
                                        type="button" onClick={handleRegister} disabled={isLoading}
                                        className="flex-1 py-3 bg-[#51B4E2] text-white rounded-md"
                                    >
                                        {isLoading ? t('loading') : t('signup')}
                                    </button>
                                    <button
                                        type="button" onClick={prevStep}
                                        className="flex-1 py-3 bg-gray-300 rounded-md"
                                    >
                                        {t('previous')}
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}