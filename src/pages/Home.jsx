/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useState, useEffect, useLayoutEffect, useRef } from 'react';
import Logo from '../assets/arabSBT_icon.png';
import BgPhone from '../assets/header_right.png';
import svg from '../assets/علاج نوبات.svg';
import svg1 from '../assets/علاج نفسي الكتروني.svg';
import svg2 from '../assets/تقنيات الاسترخاء.svg';
import svg3 from '../assets/اشعارات يوميه.svg';
import svg4 from '../assets/phone.svg';
import PhonePerspective from '../assets/phone_perspective.svg';
import TowSmartphone from '../assets/tow-smartphone.svg';
// import RightPoint from '../assets/right_point.svg';
// import LeftPoint from '../assets/left_point.svg';
// import ReviewsTop from '../assets/reviews_top.svg';
// import LogoBlue from '../assets/logo_icon_blue.svg';
// import PlusIcon from '../assets/plus.svg';
import AppleBtn from '../assets/apple_btn.svg';
import GoogleBtn from '../assets/android_btn.svg';
import { CgMenu, CgClose, CgLogOut } from 'react-icons/cg';
import { QUESTION_AND_ANSWERS } from '../utils/details';
import { FaTiktok, FaInstagram } from 'react-icons/fa';
import { IoIosArrowBack } from "react-icons/io";
import { useUser } from '../utils/UserContext';
import { motion } from 'framer-motion';
import { FaFacebookF } from 'react-icons/fa6';
import axiosInstance from '../utils/instance_axios';
import { scrollToSection } from '../utils/scrollToSection';
import ReviewsSection from '../components/ReviewsSection';

export const Header = ({
  scrollDown,
  isRTL,
  mobileDropdown,
  setMobileDropdown,
  isMobile,
  hasAccess,
  t,
  scrollContainerRef
}) => {
  const { user, handleLogout } = useUser();


  const navHref = [
    { name: t("home"), href: "#home" },
    { name: t("services"), href: "#services" },
    { name: t("about"), href: "#about" },
    { name: t('faq'), href: "#faq" },
    { name: t("contact"), href: "#contact" },
  ];

  // ✅ roles is array
  const isAdmin = user?.roles?.includes("admin");

  return (
    <header
      className={`w-full h-20 flex items-center md:px-[2%] transition-all duration-300 ease-in-out border-b border-gray-300 shadow-md
      ${scrollDown
          ? "fixed z-50 top-0 left-0 right-0 backdrop-blur-md bg-white/90 "
          : "bg-transparent"
        }`}
      dir={isRTL ? "rtl" : "ltr"}
    >
      {/* Mobile Menu Toggle */}
      <div className={`${isMobile ? "flex items-center justify-between w-full" : ""} `}>
        {isMobile && (
          <div
            className="block text-2xl text-[#51b4e2] cursor-pointer"
            onClick={() => setMobileDropdown(!mobileDropdown)}
          >
            {mobileDropdown ? <CgClose /> : <CgMenu />}
          </div>
        )}

        {/* Mobile Dropdown */}
        {mobileDropdown && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="absolute top-20 left-0 w-full bg-white shadow p-4 z-50 flex flex-col items-center justify-center"
          >
            <div className="w-full h-full flex flex-col items-center justify-center gap-4">
              {navHref.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    setMobileDropdown(false);
                    setTimeout(() => scrollToSection(item.href, scrollContainerRef?.current), 140);
                  }}
                  className="w-full flex justify-center items-center p-2 text-md md:text-lg text-start font-bold text-[#51b4e2] hover:bg-gray-100 hover:text-[#51b4e2] rounded-lg transition-colors duration-200"
                >
                  {item.name}
                </a>
              ))}
              {user ? (
                <>
                  {isAdmin ? (
                    <button
                      onClick={() => (window.location.href = "/dashboard")}
                      className="w-full flex gap-2 justify-center items-center text-[#51b4e2] hover:bg-gray-100 hover:text-[#51b4e2] font-bold py-2"
                    >
                      {t("dashboard")}
                    </button>
                  ) : !hasAccess ? (
                    <button
                      onClick={() => (window.location.href = "/subscribe")}
                      className="w-full text-center text-[#51b4e2] font-bold py-2"
                    >
                      {t("subscribe_now")}
                    </button>
                  ) : null}
                  <button
                    onClick={handleLogout}
                    className="flex items-center justify-center gap-2 w-full text-lg text-[#51b4e2] mt-2 hover:bg-red-100 hover:text-red-500 rounded-lg px-4 py-2 transition-colors duration-200"
                  >
                    <CgLogOut />
                    {t("logout")}
                  </button>
                </>
              ) : (
                <button
                  onClick={() => (window.location.href = "/login")}
                  className="w-full text-center text-[#51b4e2] font-bold py-2"
                >
                  {t("login")}
                </button>
              )}
            </div>
          </motion.div>
        )}

        {/* Logo */}
        <div className="flex items-center cursor-pointer" onClick={() => (window.location.href = "/")}>
          <img className="w-16 h-16" src={Logo} alt="Logo" loading="lazy" />
          <span className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900">
            Arab<span className="text-[#51b4e2]">CBT</span>
          </span>
        </div>
      </div>

      {/* Desktop nav */}
      {!isMobile && (
        <nav className="w-full flex justify-between items-center px-6 md:px-10 lg:px-20">
          <div className="flex items-center md:gap-2 lg:gap-4 lg:gap-6">
            {navHref.map((item, index) => (
              <a
                key={index}
                href={item.href}
                onClick={(e) => {
                  e.preventDefault();
                  scrollToSection(item.href, scrollContainerRef?.current);
                }}
                className="text-lg md:text-xl lg:text-2xl font-bold text-[#51b4e2] hover:opacity-80"
              >
                {item.name}
              </a>
            ))}
          </div>

          {user ? (
            <div className="flex items-center gap-4">
              {isAdmin ? (
                <button
                  onClick={() => (window.location.href = "/dashboard")}
                  className="text-lg text-[#51b4e2] border border-[#51b4e2] px-3 py-1 rounded-full hover:bg-[#51b4e2] hover:text-white"
                >
                  {t("dashboard")}
                </button>
              ) : !hasAccess ? (
                <button
                  onClick={() => (window.location.href = "/subscribe")}
                  className="text-lg text-[#51b4e2] border border-[#51b4e2] px-3 py-1 rounded-full hover:bg-[#51b4e2] hover:text-white"
                >
                  {t("subscribe_now")}
                </button>
              ) : null}
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 text-lg text-[#51b4e2] border border-[#51b4e2] px-3 py-1 rounded-full hover:bg-[#51b4e2] hover:text-white"
              >
                <CgLogOut />
                {t("logout")}
              </button>
            </div>
          ) : (
            <button
              onClick={() => (window.location.href = "/login")}
              className="px-6 border border-[#51b4e2] text-[#51b4e2] rounded-full hover:bg-[#51b4e2] hover:text-white"
            >
              {t("login")}
            </button>
          )}
        </nav>
      )}
    </header>
  );
};

const Button = ({ primary, secondary, bgColor }) => (
  <button className="flex justify-between items-center p-2 md:p-4 gap-2 bg-white h-[45px] md:h-[55px] text-[#51b4e2] shadow-md rounded-2xl">
    <span>{secondary}</span>
    <span className={`${bgColor || "bg-[#51b4e2]"} text-white px-2 py-1 md:px-4 md:py-2 rounded-lg`}>{primary}</span>
  </button>
);

Button.propTypes = {
  primary: PropTypes.string.isRequired,
  secondary: PropTypes.string.isRequired,
  bgColor: PropTypes.string,
};

export const Footer = ({ isMobile, isRTL, scrollContainerRef }) => {
  const { t } = useTranslation();

  const navLinks = [
    { name: t('home'), href: '#home' },
    { name: t('services'), href: '#services' },
    { name: t('about'), href: '#about' },
  ];

  const features = [
    { name: 'ادارة سهلة' },
    { name: 'ضبط المهام بشكل أفضل' },
    { name: 'الحصول على الاشعارات' },
  ];

  const socialMedia = [
    { icon: <FaFacebookF />, href: '#' },
    { icon: <FaInstagram />, href: '#' },
    { icon: <FaTiktok />, href: '#' },
  ];

  return (
    <footer
      dir={isRTL ? 'rtl' : 'ltr'}
      className="w-full bg-white border border-gray-200 text-[#333] text-sm pb-[80px]"
    >
      {/* Top Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 px-8 py-[50px]">
        {/* Brand Section */}
        <div className="flex flex-col items-start gap-5">
          <div className="flex items-center gap-4">
            <img src={Logo} alt="Logo" className="w-14 h-14 object-contain" />
            <p className="text-3xl font-bold text-black" dir="ltr">
              {isMobile ? 'Arab' : 'Salim'}
              <span className="text-[#51B4E2]">CBT</span>
            </p>
          </div>
          <p className="text-base md:text-lg leading-relaxed text-gray-700 max-w-md">
            {isMobile
              ? 'رحلتك نحو الطمأنينة والسلام الداخلي، تبدأ من هنا.'
              : 'هدفنا مساعدة أكبر عدد من الناس في جميع المراحل، للوصول للراحة النفسية والعيش حياة أفضل'}
          </p>
        </div>

        {/* Navigation & Features (hide on mobile) */}
        {!isMobile && (
          <div className="grid grid-cols-2 gap-10">
            <div>
              <h2 className="text-xl font-bold mb-4 text-[#51B4E2]">خرائط الموقع</h2>
              <ul className="space-y-2">
                {navLinks.map((item, index) => (
                  <li key={index}>
                    <a
                      href={item.href}
                      onClick={(e) => { e.preventDefault(); scrollToSection(item.href, scrollContainerRef?.current); }}
                      className="text-base text-gray-700 hover:text-[#51B4E2] hover:underline"
                    >
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h2 className="text-xl font-bold mb-4 text-[#51B4E2]">المزايا</h2>
              <ul className="space-y-2">
                {features.map((item, index) => (
                  <li key={index} className="text-base text-gray-700">
                    {item.name}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Social Section */}
        <div className={`flex flex-col ${isMobile ? 'items-center' : 'items-start'} gap-5`}>
          <h2 className="text-xl font-bold text-[#51B4E2]">
            {isMobile ? 'تابعنا على مواقع التواصل الاجتماعي' : 'تابعنا'}
          </h2>
          <div className="flex gap-4">
            {socialMedia.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="w-10 h-10 flex items-center justify-center text-white bg-[#51b4e2] rounded-full hover:bg-[#429fc8] transition-colors"
              >
                {item.icon}
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Strip */}
      <div
        className={`w-full px-8 ${isMobile
          ? 'flex flex-col items-center justify-center gap-2 text-center'
          : 'flex items-center justify-center gap-4'
          }`}
        dir={isRTL === 'ar' ? 'rtl' : 'ltr'}
      >
        <p className="text-[#51b4e2] text-sm font-semibold">
          © 2024 ArabCBT - جميع الحقوق محفوظة
        </p>
        <a
          href="https://mycardly.me/mj-group"
          target="_blank"
          rel="noopener noreferrer"
          className="text-[#51b4e2] text-sm font-semibold hover:underline flex items-center gap-1"
        >
          Power by <span className="text-teal-600">MJ Group</span> -
          <span className="text-[#51b4e2]">MOUSA JACOUB</span>
        </a>
      </div>
    </footer>
  );
};

const services = [
  {
    image: svg,
    title: "علاج نوبات الهلع والأفكار السلبية",
    description: "يمنحك التطبيق الأدوات اللازمة للسيطرة على أفكارك السلبية والتخلص من الخوف والقلق، باستخدام أحدث الأساليب العلمية المستندة إلى العلاج السلوكي المعرفي (CBT)."
  },
  {
    image: svg1,
    title: "علاج نفسي الكتروني وتوعية مستمرة",
    description: "جلسات علاج نفسي مسجلة مع المعالج النفسي سليم كيال، إلى جانب دورات وكتب حديثة تهدف إلى تطوير شخصيتك وتحسين أفكارك باستمرار."
  },
  {
    image: svg2,
    title: "تقنيات الاسترخاء العميق",
    description: "مستويات جديدة من الهدوء و جلسات الاسترخاء المتقدمة صُممت خصيصًا لتحررك من التوتر وتعيد التوازن لحياتك باستخدام تقنيات حديثة."
  },
  {
    image: svg3,
    title: "إشعارات يومية لتعزيز نفسيتك",
    description: "ستصلك الرسائل التي تحتاجها على مدار اليوم, لتحافظ على أفكارك ايجابية وتعيش بهدوء وسلام داخلي كل يوم طوال أيام السنة."
  }
];

const Home = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [openIndex, setOpenIndex] = useState(null); // Tracks the open question index
  const [scrollDown, setScrollDown] = useState(false);
  const { user } = useUser();
  const scrollRef = useRef(null);
  const [hasAccess, setHasAccess] = useState(false);
  const [loadingAccess, setLoadingAccess] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      if (!user) {
        setHasAccess(false);
        setLoadingAccess(false);
        return;
      }

      // Check if user has admin role
      if (user.roles && user.roles.includes("admin")) {
        setHasAccess(true);
        setLoadingAccess(false);
        return;
      }

      try {
        const { data } = await axiosInstance.get(`/subscriptions/check-user-subscription`, {
          params: {
            user_id: user.id
          }
        });
        setHasAccess(!!data?.active);
      } catch (error) {
        console.error("Error checking subscription:", error);
        setHasAccess(false);
      } finally {
        setLoadingAccess(false);
      }
    };

    checkAccess();
  }, [user?.id, user?.role]);

  const toggleAnswer = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const [mobileDropdown, setMobileDropdown] = useState(false);
  const [isMobile, setIsMobile] = useState();

  useLayoutEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1211);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 10;
      setScrollDown(scrolled);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // ✅ Handle initial #hash scroll with header offset
  useEffect(() => {
    if (window.location.hash) {
      setTimeout(() => scrollToSection(window.location.hash), 60);
    }
  }, []);

  // const renderStars = rating => (
  //   <div className="flex gap-[2px] mt-2">
  //     {Array.from({ length: REVIEWS_CLIENTS.length }, (_, i) =>
  //       i < rating ? (
  //         <FaStar key={i} className="text-yellow-500" />
  //       ) : (
  //         <FaRegStar key={i} className="text-gray-400" />
  //       )
  //     )}
  //   </div>
  // );

  // const TOTAL = REVIEWS_CLIENTS.length;
  // const [idx, setIdx] = useState(0);
  // const [dir, setDir] = useState(1);

  // const paginate = useCallback(d => {
  //   setDir(d);
  //   setIdx(i => (i + d + TOTAL) % TOTAL);
  // }, []);

  // useEffect(() => {
  //   const t = setInterval(() => paginate(+1), 15000);
  //   return () => clearInterval(t);
  // }, [paginate]);

  // const scrollDiv = dir => paginate(dir);

  // const card = {
  //   enter: d => ({
  //     x: d > 0 ? 150 : -150,
  //     opacity: 0,
  //   }),
  //   center: {
  //     x: 0,
  //     opacity: 1,
  //   },
  //   exit: d => ({
  //     x: d > 0 ? -150 : 150,
  //     opacity: 0,
  //   }),
  // };

  return (
    <div className="h-[100vh] flex flex-col" id='scroll-container' dir={isRTL ? 'rtl' : 'ltr'} >
      {/* ✅ pass boolean isRTL correctly */}
      <Header
        scrollDown={scrollDown}
        isRTL={isRTL}
        t={t}
        mobileDropdown={mobileDropdown}
        setMobileDropdown={setMobileDropdown}
        isMobile={isMobile}
        hasAccess={!loadingAccess && hasAccess}
        scrollContainerRef={scrollRef}   // ✅ pass ref to Header
      />

      <main ref={scrollRef} className="flex-grow overflow-y-scroll overflow-x-hidden pt-10" style={{ maxHeight: '100vh' }}>        <section id="home" className="w-full flex flex-col lg:flex-row justify-center items-center px-[4%] py-6 lg:px-[10%] " dir={isRTL ? 'ltr' : 'rtl '}>
        <div className="w-full md:w-1/2 flex justify-center h-full relative">
          <img src={BgPhone} alt="Background Phone" className="object-cover" loading='lazy' />
          <div className="absolute top-[30%] left-[14%] md:left-[0%] lg:left-[13%] xl:left-[19%]">
            <Button primary="علاج" secondary="القلق والأفكار" bgColor="bg-[#BA84D5]" />
          </div>
          <div className="absolute top-[40%] left-[58%]">
            <Button primary="راحة" secondary="نفسية دائمة" bgColor="bg-[#95C85B]" />
          </div>
          <div className="absolute top-[70%] left-[14%] md:left-[0%] lg:left-[13%] xl:left-[19%]">
            <Button primary="استرخاء" secondary="هدوء فوري" bgColor="bg-[#EF7D7A]" />
          </div>
        </div>
        <div className="w-full md:w-1/2 flex flex-col items-center p-4 text-center">
          <h1 className={`hidden lg:block text-3xl font-bold`}>Arab<span className="text-[#51b4e2]">CBT</span></h1>
          <p className="hidden lg:block text-lg my-4 text-gray-700">الطفرة الجديدة للعلاج النفسي</p>
          <p className="text-[16px] lg:text-lg text-gray-400 w-full lg:w-[45%] flex flex-col justify-center items-center gap-2">
            <span className="font-normal lg:font-bold text-black md:text-gray-400 text-center">هل تعاني من الخوف والتوتر والأفكار السلبية؟</span>
            مع ضيق في التنفس، ألم في الصدر،
            تسارع دقات القلب، وصعوبة في النوم، رغم أن جميع فحوصاتك الطبية سليمة؟
            تخلص من هذه المشكلة الآن باستخدام أفضل تقنيات العلاج النفسي.
          </p>
          <p className="text-lg font-bold text-black mt-4">احصل الآن على تجربة مجانية لمدة 7 أيام</p>
          <div className="flex flex-col justify-center items-center gap-4 mt-4">
            <p className="text-lg text-gray-700 bg-[#51b4e2] text-white font-bold px-8 py-2 rounded-lg">حمل التطبيق من هنا</p>
            <p className='mt-4 text-white p-4 rounded-lg font-bold text-lg text-center md:w-[350px] lg:w-[400px]' style={{
              background: 'linear-gradient(260deg, #51B4E2 2.75%, #198ABF 97.47%)',
              boxShadow: '0px 4px 15px 0px rgba(81, 180, 226, 0.16)'
            }}>
              تخيل أنك تبقى أسير نوبات الهلع بينما الحل بين يديك!
              لا تفوّت الفرصة لتحرير نفسك، اشترك الآن واكتسب
              الأدوات الفعّالة التي تمنحك السيطرة على مشاعرك
              وتعيد لك الحياة التي تستحقها
            </p>
          </div>
        </div>
      </section>

        <section id="services" className="scroll-mt-24 w-full px-[4%] lg:px-[10%] py-16">
          <h2 className="text-2xl font-bold text-center mb-10">على ماذا يحتوي التطبيق؟</h2>

          <div className="grid gap-6 grid-cols-1 lg:grid-cols-2" dir={isRTL ? 'rtl' : 'ltr'}>
            {services.map((data, idx) => (
              <div
                key={idx}
                className="flex items-center gap-5 rounded-xl p-6 shadow-[0_8px_30px_rgba(0,0,0,0.08)] bg-white"
              >
                <img src={data.image} alt={data.title} className="w-20 h-20 md:w-24 md:h-24 object-contain shrink-0" loading="lazy" />
                <div className="space-y-1">
                  <p className="text-xl font-bold">{data.title}</p>
                  <p className="text-gray-600">{data.description}</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        <section id="about" className="scroll-mt-24 flex flex-col md:flex-row items-center gap-10 px-[4%] lg:px-[10%] py-16" dir={isRTL ? 'ltr' : 'rtl'}>
          {/* Visual (left on desktop) */}
          <div className="w-full md:w-1/2 flex justify-center rounded-lg">

            {/* phone image on top – centered, slight tilt + shadow */}
            <div className="relative h-auto">
              <img
                src={TowSmartphone}
                alt="Tow Smartphone"
                className=""
                loading="lazy"
              />
            </div>
          </div>

          {/* Text (right on desktop) */}
          <div className="w-full md:w-1/2 px-4 md:px-8">
            <label className="block text-2xl md:text-3xl font-bold mb-4" dir={isRTL ? 'rtl' : 'ltr'}>
              كيف سيقوم التطبيق بمساعدتي في التخلص من الخوف والأفكار السبية؟
            </label>
            <p className="text-lg text-gray-700 leading-8" dir={isRTL ? 'rtl' : 'ltr'}>
              نوبة الهلع هي استجابة جسدية “Physiological response”… <span className="text-[#51b4e2] font-semibold">سيساعدك التطبيق</span> …
            </p>
          </div>
        </section>

        {/* (content unchanged below, keeping your sections/ids) */}
        <ReviewsSection id="reviews" />


        <section className="text-center mx-[4%] sm:mx-0 h-[50vh] my-14">
          <div
            className="flex flex-col rounded-lg sm:rounded-[0]  lg:flex-row w-full h-full p-6 lg:px-12"
            style={{ background: `linear-gradient(83deg, #2C2D33 1.74%, #1688BE 98.22%)` }}
          >
            <div className="lg:w-1/2 h-full lg:p-4 flex flex-col justify-center">
              <p className="text-white text-right w-[90%] lg:w-[120%] xl:w-full text-lg lg:text-xl xl:text-2xl mb-4 font-medium ">
                لا تفوت الفرصة لتحسين حياتك! قم بتحميل تطبيق Arab CBT
              </p>
              <p className="text-white text-right w-[100%] md:w-[63%] md:h-auto text-lg lg:text-xl xl:text-2xl font-medium">
                حمل و اشترك الآن وجرّب التطبيق مجانًا لمدة 7 أيام وابدأ رحلتك نحو الراحة والسعادة!
                حمل التطبيق الآن واحصل على خصم 50%. التطبيق متوفر للأندرويد والآيفون.
              </p>
              <div className="flex items-center justify-center md:justify-start gap-4 pt-6">
                <img
                  className="cursor-pointer w-28 md:w-32 transition-transform duration-300 hover:scale-105"
                  id="apple"
                  src={AppleBtn}
                  alt="Apple Store"
                  onClick={() => window.open('https://apps.apple.com/eg/app/arab-cbt/id6443894538')}
                  loading='lazy'
                />
                <img
                  className="cursor-pointer w-28 md:w-32 transition-transform duration-300 hover:scale-105"
                  id="google"
                  src={GoogleBtn}
                  alt="Google Play"
                  onClick={() => window.open('https://play.google.com/store/apps/details?id=com.arabcbt.arabcbt')}
                  loading='lazy'
                />
              </div>
            </div>

            <div className="hidden lg:relative lg:w-1/2 lg:flex lg:justify-center lg:items-center">
              <div className="absolute lg:top-[35%] xl:top-[40%] 2xl:top-[38%] lg:left-[10%] 2xl:left-[20%]" dir="ltr">
                <Button primary="علاج" secondary="القلق والأفكار" />
              </div>
              <img src={svg4} alt="Arab CBT" className="lg:mt-[18%] xl:mt-[20%] 2xl:mt-[14%] object-contain shadow-2xl rounded-b-[30px]" loading='lazy' />
            </div>
          </div>
        </section>

        <section id="faq" className="w-full flex flex-col gap-2 items-center justify-center my-14 px-[7%] md:pt-14" dir={isRTL ? 'rtl' : 'ltr'}>
          <p className="text-black text-lg md:text-2xl font-bold text-center mb-4">الأسئلة الشائعة</p>
          {QUESTION_AND_ANSWERS.map((item, index) => (
            <div key={index} className="w-full h-full px-10 flex flex-col items-center justify-center">
              <div
                className={`text-black text-sm md:text-2xl font-bold cursor-pointer w-full p-2 ${openIndex === index ? 'border border-[#51b4e2] rounded-lg' : ''}`}
                onClick={() => toggleAnswer(index)}
              >
                <div className={`flex justify-between items-center w-full text-sm md:text-2xl p-1 ${openIndex === index ? 'rounded-t-lg p-2' : 'rounded-lg'}`}>
                  {item.question}
                  <IoIosArrowBack size={30} className={`bg-blue-200 p-1 cursor-pointer rounded-full transition-transform duration-300 ${openIndex === index ? '-rotate-90' : ''}`} />
                </div>
                <motion.div
                  initial={{ height: openIndex === index ? 'auto' : 0, opacity: openIndex === index ? 1 : 0 }}
                  animate={{ height: openIndex === index ? 'auto' : 0, opacity: openIndex === index ? 1 : 0 }}
                  transition={{ duration: 1 }}
                  className={`${openIndex === index ? 'text-gray-500 font-medium text-right overflow-hidden p-4 rounded-b-lg' : 'hidden'} `}
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  {item.answer}
                </motion.div>
              </div>
            </div>
          ))}
        </section>

        <section className="w-full flex flex-col md:flex-row items-center justify-center my-14 px-[3%] hidden md:flex" dir={isRTL ? 'ltr' : 'rtl'}>
          <div className='w-full md:w-[500px]'>
            <img src={PhonePerspective} alt="" className='w-[500px] md:full h-full' />
          </div>
          <div className="w-full md:w-1/2 h-full ">
            <div className='w-full h-full flex flex-col' dir={isRTL ? 'rtl' : 'ltr'}>
              <label htmlFor="" className="text-black text-2xl md:text-4xl font-bold w-[70%] ">الراحة مش حلم … هي قرار.</label>
              <p className="text-gray-400 text-lg md:text-2xl w-[80%] mt-6">الآن، صار عندك ملاذك الآمن، حاضر معك 24/7
                وبيكبر كل يوم بمزايا وأدوات جديدة.
                لكن، كل يوم تأجيل هو يوم بيزيد فيه التوتر، وبتبعد عن
                نفسك اللي بتستحقها.
                اتخذ القرار الآن، قبل ما يسبقك الوقت.</p>
              <p className="text-black text-lg md:text-2xl font-bold w-[80%] mt-6">“اضغط الآن، مستقبلك الهادئ يبدأ هنا.”</p>
            </div>
          </div>
        </section>

        <section className="w-full flex items-center justify-center md:flex-row md:items-center md:gap-2 md:p-10 md:my-14" dir={isRTL ? 'rtl' : 'ltr'}>
          <div className='hidden  md:w-1/2 md:h-full md:flex md:flex-col md:gap-2'>
            <label htmlFor="" className="text-black text-lg md:text-2xl font-bold ">ماذا قال العملاء عن “<span className='text-[#51b4e2]'>سليم كيال</span>”</label>
            <p className="text-gray-400 text-lg md:text-2xl w-[80%] mt-10">“ هاي الكلمات مجبورة احكيلك اياها... بس بدي احكيلك شكرا كثير كثير كثير قبل مأعرفك انا مكنتش عايشة كنت عايشة بجحيم ويأس وحتى افكار انتحار. بفضل ربنا وفضلك انا اليوم موجودة بعالم ثاني رجعتلي الروح لحياتي بكل معنى الكلمة بدي احكيلك شكرا عنجد، الله يبارك فيك ويوفقك يا رب.. “</p>
          </div>

          <div className='w-full md:w-1/2'>

          </div>
        </section>

        <Footer isRTL={isRTL} isMobile={isMobile} scrollContainerRef={scrollRef} />
      </main >
    </div >
  );
};

export default Home;