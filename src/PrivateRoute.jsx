import { Navigate } from "react-router-dom";

// eslint-disable-next-line react/prop-types
const PrivateRoute = ({ children, allowedRoles }) => {
  const token = localStorage.getItem('token');
  const role = localStorage.getItem('role');

  if (!token) {
    return <Navigate to="/login" />;
  }

  // eslint-disable-next-line react/prop-types
  if (allowedRoles && !allowedRoles.some((r) => role.includes(r))) {
    return <Navigate to="/home" />;
  }

  return children;
};

export default PrivateRoute;