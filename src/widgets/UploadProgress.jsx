/* eslint-disable react/prop-types */

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
};

const UploadProgress = ({ progress, time, label = "Uploading" }) => {
  return (
    <div className="mt-4 text-center text-sm text-gray-600 space-y-2">
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
        <div
          className="bg-blue-600 h-4 rounded-full transition-all duration-200 ease-in-out"
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* Label and Timer */}
      <p>
        {label}: {progress}% | {formatTime(time)}
      </p>
    </div>
  );
};

export default UploadProgress;