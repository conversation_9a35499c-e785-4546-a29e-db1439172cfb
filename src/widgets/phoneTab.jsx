/* eslint-disable react/prop-types */

function PhoneTab({ name1, name2, className, style }) {
  return (
    <div
      className={`flex items-center bg-white text-center p-4 shadow ${className}`}
      style={{
        borderRadius: "30px",
        border: "1px solid #dee2e6",
        fontFamily: "Arial, sans-serif",
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        ...style,
      }}
    >
      {/* Large Button for Name1 */}
      <div
        className="font-bold w-[140px] text-blue-500 bg-white rounded-full text-lg ml-3"
      >
        {name1}
      </div>

      {/* Text Description for Name2 */}
      <div
        className="text-white w-[100px] text-lg font-medium bg-blue-500 rounded "
      >
        {name2}
      </div>
    </div>
  );
}

export default PhoneTab;
