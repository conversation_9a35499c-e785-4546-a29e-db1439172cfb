/* eslint-disable react/prop-types */
// src/widgets/Modal.js

/**
 * A modal component that can be used to display any content.
 *
 * @param {boolean} isOpen - Whether the modal is open or not.
 * @param {function} onClose - A function to call when the modal is closed.
 * @param {string} title - An optional title to display at the top of the modal.
 * @param {React.ReactNode} children - The content to display inside the modal.
 * @returns {React.ReactElement} The rendered modal component.
 */
const Modal = ({ isOpen, onClose, title, children }) => {
  // Return nothing if the modal is not open.
  if (!isOpen) return null;

  return (
    <>
      {/* Overlay that closes the modal on click */}
      <div
        className="fixed inset-0 bg-gray-900 opacity-50 z-40"
        onClick={onClose}
        // Close the modal on click outside
        role="button"
        tabIndex={-1}
      />

      {/* Modal box */}
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded shadow-lg w-[600px] relative">
          {/* Close icon button */}
          <button
            onClick={onClose}
            className="absolute top-2 right-2 text-gray-600 hover:text-gray-900 bg-red-200 hover:bg-red-300 rounded-full p-2 w-6 h-6 flex items-center justify-center"
            aria-label="Close modal"
          >
            &times;
          </button>

          {/* Modal title */}
          {title && <h2 className="text-lg text-center font-semibold mb-4">{title}</h2>}

          {/* Modal content */}
          <div className="modal-content">{children}</div>
        </div>
      </div>
    </>
  );
};
  
  export default Modal;
  