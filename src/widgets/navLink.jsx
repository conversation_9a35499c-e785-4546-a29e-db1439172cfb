/* eslint-disable react/prop-types */
import { NavLink, useLocation, useMatch, matchPath } from "react-router-dom";
import { useMemo } from "react";

const normalize = (p = "") => (p.endsWith("/") && p !== "/" ? p.slice(0, -1) : p);

const Nav = ({
  children,
  to,
  icon,
  childrenLinks = [],           // can be [{to,label}] or ["/path", ...]
  isRtl = false,
  exact = false,
}) => {
  const location = useLocation();
  const cleanTo = useMemo(() => normalize(to), [to]);

  // Match the base link
  const baseMatch = useMatch({ path: cleanTo, end: !!exact });

  // Match any child link (with wildcard to include subroutes)
  const childActive = useMemo(() => {
    if (!childrenLinks?.length) return false;

    return childrenLinks.some((lnk) => {
      const childTo = typeof lnk === "string" ? lnk : lnk?.to;
      if (!childTo) return false;
      const cleanChild = normalize(childTo);
      return !!matchPath({ path: `${cleanChild}/*`, end: false }, location.pathname);
    });
  }, [childrenLinks, location.pathname]);

  // If not exact, also consider subroutes of `to` as active
  const toWildcardActive = useMemo(() => {
    if (exact) return false;
    return !!matchPath({ path: `${cleanTo}/*`, end: false }, location.pathname);
  }, [cleanTo, exact, location.pathname]);

  const isActiveComputed = !!(baseMatch || childActive || toWildcardActive);

  return (
    <div className="w-full">
      <NavLink
        to={cleanTo}
        end={!!exact}
        dir={isRtl ? "rtl" : "ltr"}
        className={({ isActive: navLinkActive }) => {
          const active = isActiveComputed || navLinkActive;
          return [
            "flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-150 text-sm md:text-lg",
            "hover:text-blue-800 hover:bg-blue-100",
            active ? "bg-blue-800 text-white" : "text-white",
          ].join(" ");
        }}
      >
        <div className="flex items-center justify-center w-[30px]">
          {icon}
        </div>

        <div className={`flex items-center gap-2 ${isRtl ? "flex-row-reverse" : ""}`}>
          {children}
        </div>
      </NavLink>
    </div>
  );
};

export default Nav;