import { NavLink } from 'react-router-dom';
import { IoChevronBackSharp } from 'react-icons/io5';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';

const BookCard = ({ name, imageUrl, totalPages, averageRating, link , isBook=true}) => {
  const { t } = useTranslation();
  return (
    <div className={`${isBook ? 'w-[320px] h-[400px]' : 'w-full h-full'} flex flex-col justify-center shadow-md  shadow-gray-400 items-center rounded-lg bg-[#F2F7FF] p-4`}>
      {isBook ? <h1 className="text-2xl mb-2 font-bold  text-[#51B4E2]">{name}</h1> : null}
      <img
        src={imageUrl}
        className={`${isBook ? 'w-[190px]' : ''} object-fill rounded`}
        alt={`${name} Cover`}
      />
      {isBook ? <p className="text-[#51B4E2] mt-2">Total Pages: {totalPages}</p> : null}
      {isBook ? <p className="text-[#51B4E2]">Average Rating: {averageRating}</p> : null}
      {isBook ?
      <NavLink to={link} className="text-[#FFFFFF] bg-[#00BFA5] p-2 rounded flex items-center mt-4">
        {t('book')}
        <IoChevronBackSharp size={24} className="ml-1" />
      </NavLink> : null}
    </div>
  );
};

BookCard.propTypes = {
  name: PropTypes.string.isRequired,
  imageUrl: PropTypes.string.isRequired,
  totalPages: PropTypes.number.isRequired,
  averageRating: PropTypes.number.isRequired,
  link: PropTypes.string.isRequired,
  isBook: PropTypes.bool
};

export default BookCard;
