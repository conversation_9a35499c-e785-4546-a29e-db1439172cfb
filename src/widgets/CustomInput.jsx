import { useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { MdVisibility, MdVisibilityOff } from 'react-icons/md';

export default function CustomInput({
  id,
  label,
  type = 'text',
  value,
  onChange,
  Icon,
  isRTL,
  placeholderKey,
  iconSize = 18,
}) {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);
  const isPw = type === 'password';

  /* -------- inside CustomInput, just above the return -------- */
  const IconNode =
    Icon &&                 // prop exists
    (typeof Icon === 'function'      // passed MdEmail (component)
      ? <Icon size={iconSize} className="text-gray-400" />
      : Icon                        // passed <MdEmail /> ready-made
    );

  return (
    <div className="relative w-full mt-1">
      {/* INPUT */}
      <input
        id={id}
        name={id}
        type={isPw ? (show ? 'text' : 'password') : type}
        value={value}
        onChange={(e) => onChange(id, e.target.value)}
        aria-label={t(label)}
        placeholder={t(placeholderKey)}
        dir={isRTL ? 'rtl' : 'ltr'}
        className={`
          peer w-full mt-4 p-3 rounded-md border border-gray-300 bg-white
          focus:outline-none focus:ring-2 focus:ring-[#51B4E2]
          ${Icon || isPw ? (isRTL ? 'pr-10' : 'pl-10') : ''}
        `}
      />

      {/* LABEL */}
      <label
        htmlFor={id}
        className={`absolute ${isRTL ? 'right-3' : 'left-3'}
          -translate-y-1/2 top-10 flex items-center gap-1 text-gray-600
          transition-all duration-300 ease-in-out
          peer-focus:-translate-y-10 peer-focus:scale-90 peer-focus:text-[#51B4E2]
          peer-[&:not(:placeholder-shown)]:-translate-y-10
          peer-[&:not(:placeholder-shown)]:scale-90
          peer-focus:bg-white peer-focus:px-1 peer-focus:border
          peer-focus:border-[#51B4E2] peer-focus:rounded-md
          peer-[&:not(:placeholder-shown)]:bg-white
          peer-[&:not(:placeholder-shown)]:px-1
          peer-[&:not(:placeholder-shown)]:border
          peer-[&:not(:placeholder-shown)]:border-[#51B4E2]
          peer-[&:not(:placeholder-shown)]:rounded-md
        `}
      >
        {IconNode}
        <span>{t(label)}</span>
      </label>

      {/* SHOW/HIDE PASSWORD */}
      {isPw && (
        <button
          type="button"
          onClick={() => setShow(!show)}
          className={`absolute ${isRTL ? 'left-3' : 'right-3'}
            top-10 -translate-y-1/2 text-gray-500`}
        >
          {show ? <MdVisibilityOff size={22} /> : <MdVisibility size={22} />}
        </button>
      )}
    </div>
  );
}

/* PropTypes */
CustomInput.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  type: PropTypes.string,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  Icon: PropTypes.elementType,      // ← מקבל MdEmail/MdPhone ולא ReactElement
  isRTL: PropTypes.bool,
  placeholderKey: PropTypes.string,
  iconSize: PropTypes.number,
};