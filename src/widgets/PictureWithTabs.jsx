import phoneImage from "../assets/header_right.png"; // Ensure correct image path
import PhoneTab from "./PhoneTab";

function PictureWithTabs() {
  return (
    <div className="relative -z-10">
      <img src={phoneImage} alt="phone" />

      {/* Tabs */}
      <div className="z-30">
        {/* Tab 1 */}
        <PhoneTab
          className="absolute top-[35%] left-[30%] transform -translate-x-1/2 -translate-y-1/2"
          name2="علاج"
          name1="القلق والأفكار"
        />
        {/* Tab 2 */}
        <PhoneTab
          className="absolute top-[55%] left-[75%] transform -translate-x-1/2 -translate-y-1/2"
          name2="راحة"
          name1="نفسية دائمة"
        />
        {/* Tab 3 */}
        <PhoneTab
          className="absolute top-[75%] left-[30%] transform -translate-x-1/2 -translate-y-1/2"
          name2="استرخاء"
          name1="هدوء فوري"
        />
      </div>
    </div>
  );
}

export default PictureWithTabs;
