/* eslint-disable react/prop-types */
import { useState } from "react";
import { CgChevronDown, CgCheck } from "react-icons/cg";
import { useTranslation } from "react-i18next";

export const SubscriptionDropdown = ({ selected, setSelected }) => {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    
    const subscriptionTypes = [
        { label: t("none"), value: "none" },
        { label: t("basic"), value: "basic" },
        { label: t("advanced"), value: "advanced" },
        { label: t("advanced_plus"), value: "advanced_plus" }
    ];
    return (
        <div className="relative">
            {/* Dropdown button */}
            <div
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer flex justify-between items-center"
                onClick={() => setIsOpen(!isOpen)}
            >
                {selected ? subscriptionTypes.find(type => type.value === selected)?.label : t("select_subscription_type")}
                <CgChevronDown className={`transition-transform duration-300 ${isOpen ? "rotate-180" : ""}`} />
            </div>

            {/* Dropdown options */}
            {isOpen && (
                <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md mt-1 shadow-md">
                    {subscriptionTypes.map((type) => (
                        <div
                            key={type.value}
                            className={`px-3 py-2 cursor-pointer flex items-center justify-between hover:bg-gray-200 transition ${
                                selected === type.value ? "bg-gray-300" : ""
                            }`}
                            onClick={() => {
                                setSelected(type.value);
                                setIsOpen(false);
                            }}
                        >
                            {type.label}
                            {selected === type.value && <CgCheck />}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};
