import PropTypes from 'prop-types';
import BookCard from './bookCard'; // Adjust the path as needed

const BookGrid = ({ name, imageUrl, totalPages, averageRating, link }) => {
  return (
    <BookCard
      name={name}
      imageUrl={imageUrl}
      totalPages={totalPages}
      averageRating={averageRating}
      link={link}
    />
  );
};

// Define correct PropTypes for each prop
BookGrid.propTypes = {
  name: PropTypes.string.isRequired,        // Book name should be a string
  imageUrl: PropTypes.string.isRequired,    // Image URL should be a string
  totalPages: PropTypes.number.isRequired,  // Total pages should be a number
  averageRating: PropTypes.number.isRequired, // Average rating should be a number
  link: PropTypes.string.isRequired         // Link should be a string
};

export default BookGrid;
