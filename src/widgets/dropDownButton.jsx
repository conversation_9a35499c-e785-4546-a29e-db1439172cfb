/* eslint-disable react/prop-types */
 import {  useState } from "react";
 const DropDownButton = ({ label, options, selectedValue, onChange, placeholder }) => {
    const [isOpen, setIsOpen] = useState(false);

    const handleSelect = (value) => {
        onChange(value);
        setIsOpen(false);
    };

    return (
        <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
            <div
                className="w-full px-4 py-2 border border-gray-300 rounded-lg cursor-pointer focus:outline-none focus:ring-2 focus:ring-teal-500"
                onClick={() => setIsOpen((prev) => !prev)}
            >
                {selectedValue ? options.find((o) => o.id === selectedValue)?.text : placeholder}
            </div>
            {isOpen && (
                <div className="absolute mt-1 h-[130px] w-full bg-white border border-gray-300 rounded-lg shadow-lg z-40 overflow-auto max-h-64">
                    {options.map((option) => (
                        <div
                            key={option.id}
                            className="px-4 py-2 hover:bg-teal-500 hover:text-white cursor-pointer"
                            onClick={() => handleSelect(option.id)}
                        >
                            {option.text}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default DropDownButton;




