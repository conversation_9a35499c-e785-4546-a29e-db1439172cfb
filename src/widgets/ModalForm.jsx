/* eslint-disable react/prop-types */
// ModalForm.jsx
import { CgClose } from "react-icons/cg";

const ModalForm = ({
    isEditing,
    formState,
    setFormState,
    handleSubmit,
    closeModal,
    t,
}) => {
    return (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-[600px]">
                <div className="relative flex items-center mb-4">
                    <button className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full" onClick={closeModal}>
                        <CgClose />
                    </button>
                    <h2 className="text-lg font-semibold">
                        {isEditing ? t("edit_lecture") : t("add_lecture")}
                    </h2>
                </div>

                <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                    <div className="flex flex-col gap-2">
                        <label className="font-medium text-gray-700">{t("title")}</label>
                        <input
                            type="text"
                            value={formState.title}
                            onChange={(e) => setFormState({ ...formState, title: e.target.value })}
                            required
                            className="w-full p-2 border border-gray-300 rounded-md"
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <label className="font-medium text-gray-700">{t("description")}</label>
                        <input
                            type="text"
                            value={formState.description}
                            onChange={(e) => setFormState({ ...formState, description: e.target.value })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <label className="font-medium text-gray-700">{t("image")}</label>
                        <input
                            type="file"
                            accept="video/*"
                            onChange={(e) => setFormState({ ...formState, image: e.target.files[0] })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                        />
                    </div>

                    <button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-md">
                        {isEditing ? t("update") : t("save")}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default ModalForm;
