import { useState, useEffect } from 'react'
import logo from '../assets/arab_cbt_logo.png';
import { useTranslation } from 'react-i18next';

const ProgressBarWidget = () => {
    const { t } = useTranslation();
    const [progress, setProgress] = useState(0);

    useEffect(() => {
      const progressTimer = setInterval(() => {
        setProgress((prevProgress) => {
          if (prevProgress >= 100) {
            clearInterval(progressTimer);
            return 100;
          }
          return prevProgress + 5; // Increment progress by 5%
        });
      }, 148); // Update every 500ms
  
      return () => clearInterval(progressTimer); // Cleanup on component unmount
    }, []);
  
    return (
      <div className="m-0 p-0 h-screen w-full flex flex-col justify-center items-center">
        <img src={logo} alt="App Logo" />
        <div className="text-3xl font-bold mt-4">
          {t('name_app_1')}
          <span className="text-[#51B4E2]">{t('name_app_2')}</span>
        </div>
        <div className="relative w-56 mt-4 h-2 bg-gray-200 rounded">
          <div
            className="absolute top-0 left-0 h-2 rounded bg-[#51B4E2] transition-all duration-100"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>
    );
}

export default ProgressBarWidget