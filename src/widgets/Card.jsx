
import PropTypes from 'prop-types';

const Card = ({ title, subtitle, icon, bgColor, textColor }) => {
  return (
    <div
      className={`h-[72px] px-[18px] py-2 ${bgColor} rounded-xl justify-center items-center gap-3 inline-flex`}
    >
      <div className="justify-start items-center gap-3 flex">
        <div className="w-[172px] flex-col justify-center items-end inline-flex">
          <div
            className={`text-right ${textColor} text-lg font-bold font-['Tajawal']`}
          >
            {title}
          </div>
          <div
            className={`opacity-80 text-right ${textColor} text-xs font-normal font-['<PERSON><PERSON>wal'] leading-none`}
          >
            {subtitle}
          </div>
        </div>
        <div className="w-[38px] h-[38px] relative">
          {/* Icon or image */}
          {icon || <img src={icon} alt="icon" className="w-full h-full object-cover" />}
        </div>
      </div>
    </div>
  );
};

Card.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  icon: PropTypes.string,
  bgColor: PropTypes.string,
  textColor: PropTypes.string,
};

Card.defaultProps = {
  bgColor: 'bg-[#51b4e2]',
  textColor: 'text-white',
  icon: null,
};

export default Card;
