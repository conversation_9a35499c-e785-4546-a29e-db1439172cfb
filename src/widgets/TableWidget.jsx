/* eslint-disable react/prop-types */
import { useState, useMemo, useRef } from 'react';
import { FaPlus } from 'react-icons/fa';
import { MdNoteAdd, MdOutlineArrowBackIos, MdOutlineArrowForwardIos } from 'react-icons/md';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';

const ALIGN_CLASS = { left: 'text-left', center: 'text-center', right: 'text-right' };

// Truncate helper – מקצר טקסט ל-X שורות (עם ellipsis) + tooltip אופציונלי
function Truncate({ children, lines = 1, tooltip = true }) {
  if (children == null) return '–';

  // אם זה React element – משאירים אותו כמו שהוא
  if (typeof children === 'object' && children.$$typeof) return children;

  const text = String(children);

  // אם lines<=0 – לא לקצר
  if (!Number.isFinite(lines) || lines <= 0) return text;

  const style = {
    display: '-webkit-box',
    WebkitLineClamp: lines,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    lineHeight: '1.35',
    maxHeight: `${lines * 1.35}em`,
    whiteSpace: 'normal',
    wordBreak: 'break-word',
  };

  return (
    <span style={style} title={tooltip ? text : undefined}>
      {text}
    </span>
  );
}

const TableWidget = ({
  title,
  columns = [],
  rows = [],
  isAddClick = true,
  onAddClick,
  isAddNote = false,
  onAddNoteClick,
  stickyFirstCol = false,
  pageSize = 8,
  px,
  isMobile = false,
  vh = 100,

  // ▼ חדש: שליטה גלובלית בקיצור
  clampLines = 1,
  ellipsisTooltip = true,
}) => {
  const { i18n, t } = useTranslation();
  const dir = i18n.dir();

  const [currentPage, setCurrentPage] = useState(1);
  const [colWidths, setColWidths] = useState(() => columns.map(() => 100 / Math.max(columns.length || 1, 1)));

  const tableRef = useRef(null);
  const resizingColIndex = useRef(null);

  const safeRows = useMemo(() => (Array.isArray(rows) ? rows.filter(Boolean) : []), [rows]);
  const finalPageSize = isMobile ? 5 : pageSize;
  const totalPages = Math.max(1, Math.ceil(safeRows.length / finalPageSize));
  const pagedRows = safeRows.slice((currentPage - 1) * finalPageSize, currentPage * finalPageSize);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) setCurrentPage(page);
  };

  // התחלת שינוי רוחב
  const startResize = (e, index) => {
    resizingColIndex.current = index;
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', stopResize);
  };

  // שינוי בפועל
  const handleResize = (e) => {
    if (resizingColIndex.current === null || !tableRef.current) return;
    const tableRect = tableRef.current.getBoundingClientRect();
    const totalWidth = tableRect.width;

    const newWidths = [...colWidths];
    const beforePercent = colWidths.slice(0, resizingColIndex.current + 1).reduce((a, b) => a + b, 0);
    const colLeftPx = tableRect.left + (beforePercent / 100) * totalWidth;

    const deltaPx = e.clientX - colLeftPx;
    const deltaPercent = (deltaPx / totalWidth) * 100;

    // שמירה בסיסית שלא יהיו ערכים שליליים/מוגזמים
    const i = resizingColIndex.current;
    const next = i + 1;
    if (newWidths[i] + deltaPercent < 5 || newWidths[next] - deltaPercent < 5) return;

    newWidths[i] += deltaPercent;
    newWidths[next] -= deltaPercent;

    setColWidths(newWidths);
  };

  // סיום שינוי
  const stopResize = () => {
    resizingColIndex.current = null;
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
  };

  const alignToClass = (align) => ALIGN_CLASS[align] || ALIGN_CLASS.center;

  // פונקציית רנדר לתא – מוסיפה קיצור לפי כללים
  const renderCell = (col, row, rowIndex) => {
    const raw = typeof col.render === 'function'
      ? (col.render.length >= 2 ? col.render(row[col.key], row, rowIndex) : col.render(row[col.key]))
      : row[col.key];

    // אם המשתמש סימן noClamp לעמודה – אל תקצר
    if (col.noClamp) return raw ?? '–';

    // קביעת מספר שורות לקיצור: פר-עמודה או גלובלי
    const lines = Number.isFinite(col.clampLines) ? col.clampLines : clampLines;

    // אם זה React element – נחזיר כמו שהוא (למשל כפתורים/אודיו)
    if (raw && typeof raw === 'object' && raw.$$typeof) return raw;

    // מחרוזת/מספר/בוליאני – נקצר
    if (typeof raw === 'string' || typeof raw === 'number' || typeof raw === 'boolean') {
      return <Truncate lines={lines} tooltip={ellipsisTooltip}>{String(raw)}</Truncate>;
    }

    // אובייקט/מערך – נציג כ־JSON מקוצר
    if (raw != null) {
      try {
        return <Truncate lines={lines} tooltip={ellipsisTooltip}>{JSON.stringify(raw)}</Truncate>;
      } catch {
        return '–';
      }
    }

    return '–';
  };

  return (
    <div
      className={`relative widget bg-white rounded-lg shadow-[0px_0px_20px_5px_rgba(0,0,0,0.2)] ${isMobile ? 'p-2' : 'p-4'} flex flex-col`}
      style={{ minHeight: `${vh}vh`, height: '100%', fontSize: px ? `${px}px` : undefined }}
      dir={dir}
    >
      {/* Header */}
      <div className={`flex ${isMobile ? 'flex-col' : 'flex-row justify-between'} border-b pb-2 mb-4`}>
        <h3 className="font-semibold">{title}</h3>
        <div className="flex items-center gap-2">
          {isAddNote && (
            <button onClick={onAddNoteClick} className="bg-yellow-500 text-white px-2 py-1 rounded">
              <MdNoteAdd size={isMobile ? 16 : 24} />
            </button>
          )}
          {isAddClick && (
            <button onClick={onAddClick} className="bg-blue-600 text-white px-2 py-1 rounded">
              <FaPlus size={isMobile ? 16 : 24} />
            </button>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto">
        <table
          ref={tableRef}
          className="border-collapse w-full table-fixed"
          style={{ tableLayout: 'fixed' }} // חשוב לקיצור לפי רוחב התא
        >
          <thead className="bg-gray-100 sticky top-0 left-0 right-0 z-10">
            <tr>
              {columns.map((col, i) => (
                <th
                  key={i}
                  className={`relative p-2 border font-semibold ${alignToClass(col.align)}`}
                  style={{
                    width: `${colWidths[i]}%`,
                    position: stickyFirstCol && i === 0 ? 'sticky' : undefined,
                    [dir === 'rtl' ? 'right' : 'left']: stickyFirstCol && i === 0 ? 0 : undefined,
                    background: stickyFirstCol && i === 0 ? '' : undefined,
                    zIndex: stickyFirstCol && i === 0 ? 20 : undefined
                  }}
                >
                  {col.label}
                  {i < columns.length - 1 && (
                    <div
                      onMouseDown={(e) => startResize(e, i)}
                      className="absolute top-0 right-0 cursor-col-resize h-full"
                      style={{ width: '8px', transform: 'translateX(50%)' }}
                      title="Resize"
                    />
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {pagedRows.length > 0 ? (
              <AnimatePresence>
                {pagedRows.map((row, rowIndex) => (
                  <motion.tr
                    key={rowIndex}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                  >
                    {columns.map((col, ci) => (
                      <td
                        key={ci}
                        className={`p-1 border ${alignToClass(col.align)}`}
                        style={{
                          width: `${colWidths[ci]}%`,
                          minHeight: '60px',
                          height: '60px',
                          [dir === 'rtl' ? 'right' : 'left']: stickyFirstCol && ci === 0 ? 0 : undefined,
                          background: stickyFirstCol && ci === 0 ? '#fff' : undefined,
                          zIndex: stickyFirstCol && ci === 0 ? 10 : undefined
                        }}
                      >
                        {renderCell(col, row, rowIndex)}
                      </td>
                    ))}
                  </motion.tr>
                ))}
              </AnimatePresence>
            ) : (
              <tr>
                <td colSpan={columns.length} className="py-6 px-6 border text-center text-gray-400">
                  {t('no_data_available')}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {safeRows.length > finalPageSize && (
        <div className="mt-4 flex justify-center items-center gap-2 text-sm">
          <button className="p-2 rounded-lg text-gray-600 hover:text-gray-800 border border-gray-300 flex items-center justify-center" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1}>
            <MdOutlineArrowForwardIos />
          </button>
          <span className="px-3 py-1 rounded-lg text-gray-600 hover:text-gray-800 border border-gray-300 flex items-center justify-center">{currentPage} / {totalPages}</span>
          <button className="p-2 rounded-lg text-gray-600 hover:text-gray-800 border border-gray-300 flex items-center justify-center" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages}>
            <MdOutlineArrowBackIos />
          </button>
        </div>
      )}
    </div>
  );
};

export default TableWidget;