import PropTypes from "prop-types"; // ✅ Correct import

const DeleteConfirmationModal = ({ isOpen, onClose, onConfirm, title, subTitle, cancelBtn, confirmBtn }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg w-[400px] text-center">
                <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
                <p className="text-gray-600 mt-2">{subTitle}</p>
                <div className="mt-6 flex justify-center gap-4">
                    <button
                        className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition"
                        onClick={onClose}
                    >
                        {cancelBtn}
                    </button>
                    <button
                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition"
                        onClick={onConfirm}
                    >
                        {confirmBtn}
                    </button>
                </div>
            </div>
        </div>
    );
};

// ✅ Define PropTypes for the component
DeleteConfirmationModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    title: PropTypes.string.isRequired,
    subTitle: PropTypes.string.isRequired,
    cancelBtn: PropTypes.string.isRequired,
    confirmBtn: PropTypes.string.isRequired
};

export default DeleteConfirmationModal;
