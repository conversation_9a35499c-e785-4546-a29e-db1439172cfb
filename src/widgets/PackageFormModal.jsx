/* eslint-disable react/prop-types */
import { Modal, Box, TextField, Button } from '@mui/material';
import { useState, useEffect } from 'react';

const PackageFormModal = ({ open, onClose, onSave, editingPackage }) => {
  const [form, setForm] = useState({
    name: '',
    monthly_price: '',
    yearly_price: ''
  });

  useEffect(() => {
    if (editingPackage) {
      setForm({
        name: editingPackage.name,
        monthly_price: editingPackage.monthly_price,
        yearly_price: editingPackage.yearly_price
      });
    } else {
      setForm({ name: '', monthly_price: '', yearly_price: '' });
    }
  }, [editingPackage]);

  const handleChange = (field, value) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    onSave(form);
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md mx-auto mt-20">
        <h2 className="text-xl font-bold mb-4">{editingPackage ? 'تعديل باقة' : 'إضافة باقة'}</h2>
        <TextField label="اسم الباقة" fullWidth margin="normal" value={form.name} onChange={(e) => handleChange('name', e.target.value)} />
        <TextField label="سعر شهري" type="number" fullWidth margin="normal" value={form.monthly_price} onChange={(e) => handleChange('monthly_price', e.target.value)} />
        <TextField label="سعر سنوي" type="number" fullWidth margin="normal" value={form.yearly_price} onChange={(e) => handleChange('yearly_price', e.target.value)} />
        <div className="flex justify-end gap-4 mt-4">
          <Button onClick={onClose}>إلغاء</Button>
          <Button variant="contained" onClick={handleSubmit}>{editingPackage ? 'تحديث' : 'حفظ'}</Button>
        </div>
      </Box>
    </Modal>
  );
};

export default PackageFormModal;