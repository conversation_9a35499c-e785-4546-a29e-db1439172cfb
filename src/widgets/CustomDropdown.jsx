/* eslint-disable react/prop-types */
import { useState } from 'react';

const CustomDropdown = ({ question, questions, setQuestions, i18n, t, name }) => {
    const [isOpen, setIsOpen] = useState(false); // Tracks dropdown state
    const options = [
        { value: 'question', label: t('question') },
        { value: 'introduction', label: t('introduction') },
        { value: 'sub_question', label: t('sub_question') },
        { value: 'conclusion', label: t('conclusion') },
        { value: 'sentence', label: t('sentence') },
        { value: 'repeating_sentence', label: t('repeating_sentence') },
        { value: 'sentence_general', label: t('sentence_general') },
        { value: 'summary', label: t('summary') },
    ];

    const handleSelect = (value) => {
        // Update question type on selection
        const updatedQuestions = [...questions];
        const index = updatedQuestions.findIndex((q) => q.id === question.id);
        updatedQuestions[index][name] = value;

        // Update state with the new question data
        setQuestions(updatedQuestions);
        setIsOpen(false); // Close dropdown
    };

    return (
        <div className="relative w-full">
            {/* Dropdown Trigger */}
            <div
                className={`w-full px-2 py-1 border border-gray-300 rounded-md cursor-pointer ${
                    i18n.language === 'ar' || i18n.language === 'he' ? 'text-right' : 'text-left'
                }`}
                dir={i18n.language === 'ar' || i18n.language === 'he' ? 'rtl' : 'ltr'}
                onClick={() => setIsOpen(!isOpen)} // Toggle dropdown
            >
                {options.find((opt) => opt.value === question[name])?.label || t('question')}
            </div>

            {/* Dropdown Options */}
            {isOpen && (
                <div className="absolute mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-10">
                    {options.map((option) => (
                        <div
                            key={option.value}
                            onClick={() => handleSelect(option.value)}
                            className="px-2 py-1 hover:bg-gray-100 cursor-pointer"
                        >
                            {option.label}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default CustomDropdown;
