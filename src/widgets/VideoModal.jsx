/* eslint-disable react/prop-types */
// VideoModal.jsx

import { CgClose } from "react-icons/cg";

const VideoModal = ({ selectedVideo, closeModal }) => {
    return (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-[800px] h-[80%] relative">
                <button
                    className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full"
                    onClick={closeModal}
                >
                    <CgClose />
                </button>
                
                <video controls autoPlay className="w-full h-full rounded-md">
                    <source src={selectedVideo} type="video/webm" />
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
    );
};

export default VideoModal;
