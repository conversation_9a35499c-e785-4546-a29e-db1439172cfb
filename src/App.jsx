
import { Routes, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import Splash from "./pages/Splash";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Home from "./pages/Home";
import Admin from "./pages/admin/Dashboard/Admin";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import Dashboard from "./pages/admin/Dashboard";
import { Toaster } from "react-hot-toast";
import Questionnaire from "./pages/client/Questionnaire";
import PrivateRoute from "./PrivateRoute";

// Lazy load components
const Settings = lazy(() => import("./pages/admin/Settings"));
const Profile = lazy(() => import("./pages/admin/Profile"));
import RegisterAdmin from "./pages/admin/registerAdmin";
import NotFound from "./NotFound.jsx";
const Support = lazy(() => import("./pages/Support"));
const Books = lazy(() => import("./pages/admin/Books"));
const PreviewOfBooks = lazy(() => import("./pages/admin/PreviewOfBooks"))
const PagesOfBooks = lazy(() => import("./pages/admin/PagesOfBooks"))
const Concerns = lazy(() => import("./pages/admin/concerns"))
const TreatmentVideo = lazy(() => import("./pages/admin/TreatmentVideo"))
const OnlineTreatment = lazy(() => import("./pages/admin/TreatmentOnline"))
const TreatmentItemOnline = lazy(() => import("./pages/admin/TreatmentItemOnline.jsx"))
const Topics = lazy(() => import("./pages/admin/Topics"))
const AddPages = lazy(() => import("./pages/admin/AddPages"));
const Thoughts = lazy(() => import("./pages/admin/Thoughts"));
const Questions = lazy(() => import("./pages/admin/Questions"));
const AdminSubscriptionPackages = lazy(() => import("./pages/admin/Packages"));
const PackageFeatures = lazy(() => import("./pages/admin/PackageFeatures"));
const Features = lazy(() => import("./pages/admin/Features"));
const DistractionActivities = lazy(() => import("./pages/admin/DistractionActivities"));
const DistractionAudio = lazy(() => import("./pages/admin/DistractionAudio"));
const Meditation = lazy(() => import("./pages/admin/Meditation"));
const MeditationAudios = lazy(() => import("./pages/admin/MeditationAudios"));
const TopicsNotification = lazy(() => import("./pages/admin/topicsNotification"));
const Lectures = lazy(() => import("./pages/admin/Lectures"))
const LectureDetails = lazy(() => import("./pages/admin/LectureDetails"))
const AdminBookPurchases = lazy(() => import("./pages/admin/AdminBookPurchases"))
const Subscribe = lazy(() => import("./pages/Subscribe"));
const Users = lazy(() => import("./pages/admin/Users"));

function App() {

  return (
    <>
      <Toaster
        position="bottom-right"
        reverseOrder={false}
      />
      <Routes>
        <Route path="/" element={<Splash />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/subscribe" element={<Subscribe />} />
        <Route path="/support" element={<Support />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password/:token" element={<ResetPassword />} />
        <Route path="/registerAdmin" element={<RegisterAdmin />} />
        <Route path="/home" element={<Home />} />
        <Route path="/questionnaire/:thoughtId" element={<Questionnaire />} />
        <Route path="/dashboard" element={
          <PrivateRoute>
            <Admin />
          </PrivateRoute>
        }>
          {/* Child routes for the dashboard */}
          <Route index element={<Dashboard />} />
          <Route path="users" element={<Suspense fallback={null}><Users /></Suspense>} />
          <Route path="settings" element={<Suspense fallback={null}><Settings /></Suspense>} />
          <Route path="profile" element={<Suspense fallback={null}><Profile /></Suspense>} />
          <Route path="books" element={<Suspense fallback={null}><Books /></Suspense>} />
          <Route path="add-pages/:bookId/:name" element={<Suspense fallback={null}><AddPages /></Suspense>} />
          <Route path="pages/:bookId" element={<Suspense fallback={null}><PagesOfBooks /></Suspense>} />
          <Route path="preview/:bookId" element={<Suspense fallback={null}><PreviewOfBooks /></Suspense>} />
          <Route path="topics" element={<Suspense fallback={null}><Topics /></Suspense>} />
          <Route path="topics/:id/:name/notification" element={<Suspense fallback={null}><TopicsNotification /></Suspense>} />
          <Route path="concerns" element={<Suspense fallback={null}><Concerns /></Suspense>} />
          <Route path="thoughts/:id" element={<Suspense fallback={null}><Thoughts /></Suspense>} />
          <Route path="questions/thoughts/:thoughtId" element={<Suspense fallback={null}><Questions /></Suspense>} />
          <Route path="online-treatment" element={<Suspense fallback={null}><OnlineTreatment /></Suspense>} />
          <Route path="online-treatment/treatment-video/:id" element={<Suspense fallback={null}><TreatmentVideo /></Suspense>} />
          <Route path="online-treatment/treatment-video/treatment-items/:id" element={<Suspense fallback={null}><TreatmentItemOnline /></Suspense>} />          <Route path="distractions" element={<Suspense fallback={null}><DistractionActivities /></Suspense>} />
          <Route path="distractions/:id" element={<Suspense fallback={null}><DistractionAudio /></Suspense>} />
          <Route path="subscriptions" element={<Suspense fallback={null}><AdminSubscriptionPackages /></Suspense>} />
          <Route path="subscriptions/:package_id/features" element={<Suspense fallback={null}><PackageFeatures /></Suspense>} />
          <Route path="features" element={<Suspense fallback={null}><Features /></Suspense>} />
          <Route path="book-purchases" element={<Suspense fallback={null}><AdminBookPurchases /></Suspense>} />
          <Route path="meditation" element={<Suspense fallback={null}><Meditation /></Suspense>} />
          <Route path="meditation/audio/:id" element={<Suspense fallback={null}><MeditationAudios /></Suspense>} />
          <Route path="lectures" element={<Suspense fallback={null}><Lectures /></Suspense>} />
          <Route path="lectures/:id" element={<Suspense fallback={null}><LectureDetails /></Suspense>} />
        </Route>

        <Route path="*" element={<NotFound />} />
      </Routes>
    </>

  );
};

export default App;