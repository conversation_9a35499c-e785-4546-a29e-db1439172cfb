{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "aos": "^2.3.4", "audio-stream": "^0.2.0", "axios": "^1.7.7", "framer-motion": "^12.4.7", "html-flip-book-react": "^0.0.0-alpha.1", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.1", "jwt-decode": "^4.0.0", "motion": "^12.7.4", "papaparse": "^5.5.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-flip-page": "^1.6.4", "react-hot-toast": "^2.4.1", "react-i18next": "^15.0.2", "react-icons": "^5.3.0", "react-loader-spinner": "^6.1.6", "react-pageflip": "^2.0.3", "react-prop-types": "^0.4.0", "react-quill": "^2.0.0", "react-router-dom": "^6.26.2", "react-toastify": "^10.0.5", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "vite": "^5.4.1"}}